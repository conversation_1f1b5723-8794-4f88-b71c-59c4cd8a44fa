{"name": "uipart", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "main": "main.js", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:electron": "node build-electron.js", "electron": "electron .", "electron:dev": "ng build && electron .", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^20.0.0", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/platform-browser-dynamic": "^20.0.0", "@angular/router": "^20.0.0", "@capacitor/app": "7.0.1", "@capacitor/core": "7.4.2", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/angular": "^8.6.5", "electron-updater": "^6.6.2", "ionicons": "^7.0.0", "pouchdb": "^9.0.0", "pouchdb-browser": "^9.0.0", "pouchdb-find": "^9.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.0", "@angular-eslint/builder": "^20.0.0", "@angular-eslint/eslint-plugin": "^20.0.0", "@angular-eslint/eslint-plugin-template": "^20.0.0", "@angular-eslint/schematics": "^20.0.0", "@angular-eslint/template-parser": "^20.0.0", "@angular/cli": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "@angular/language-service": "^20.0.0", "@capacitor/cli": "7.4.2", "@ionic/angular-toolkit": "^12.0.0", "@types/jasmine": "~5.1.0", "@types/pouchdb": "^6.4.2", "@types/pouchdb-find": "^7.3.3", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "electron-packager": "^17.1.2", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.0"}, "description": "An Ionic project", "build": {"appId": "com.yourapp.id", "productName": "uiPart", "directories": {"output": "dist-electron"}, "files": ["dist/", "main.js", "package.json"], "win": {"target": "portable", "icon": "icon.ico"}}, "keywords": [], "license": "ISC"}