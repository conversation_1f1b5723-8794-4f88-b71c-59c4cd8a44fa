.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  
  ion-spinner {
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
}

ion-card {
  margin: 1rem;
  
  ion-card-header {
    padding-bottom: 0.5rem;
  }
  
  ion-card-title {
    font-size: 1.2rem;
    font-weight: 600;
  }
  
  ion-card-subtitle {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
}

.search-results {
  margin-top: 1rem;
  
  h4 {
    margin: 1rem 0 0.5rem 0;
    color: var(--ion-color-primary);
    font-weight: 600;
  }
}

.sample-data {
  h4 {
    margin: 1.5rem 0 0.5rem 0;
    color: var(--ion-color-primary);
    font-weight: 600;
    border-bottom: 1px solid var(--ion-color-light);
    padding-bottom: 0.5rem;
  }
  
  ion-item {
    --padding-start: 0;
    
    ion-label {
      h3 {
        font-size: 1rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
      }
      
      p {
        font-size: 0.85rem;
        color: var(--ion-color-medium);
        margin: 0;
      }
    }
  }
}

.stats-grid {
  ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
    
    ion-label {
      text-align: center;
      
      h3 {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--ion-color-medium);
        margin-bottom: 0.25rem;
      }
      
      p {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--ion-color-primary);
        margin: 0;
      }
    }
  }
}

ion-select {
  max-width: 200px;
}

ion-input {
  --padding-start: 0;
}

// Responsive design
@media (max-width: 768px) {
  ion-card {
    margin: 0.5rem;
  }
  
  .stats-grid {
    ion-col {
      padding: 0.25rem;
    }
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .sample-data h4 {
    border-bottom-color: var(--ion-color-dark);
  }
}
