// load-split.js
const fs = require('fs');
const crypto = require('crypto');
const PouchDB = require('pouchdb');
PouchDB.plugin(require('pouchdb-find'));

// ==== Helpers ==== //

/**
 * Derive a stable identifier for a given item, falling back to an MD5
 * of its JSON if no obvious ID field exists.
 */
function stableIdForItem(key, item) {
  const baseId =
    item.Id ?? item.id ??
    item[key + '_Id'] ?? item[key + '_id'] ??
    item.Code ?? item.code;

  if (baseId != null) {
    return String(baseId);
  }
  return crypto.createHash('md5').update(JSON.stringify(item)).digest('hex');
}

/**
 * Converts your specific JSON shape:
 * { data: [ { tblpatientcomplaints: [ ... ], otherTable: [...] }, ... ] }
 * into an array of PouchDB documents.
 */
function jsonToDocs(top) {
  const docs = [];

  if (!top || !Array.isArray(top.data)) {
    throw new Error('Expected top-level object with "data" array');
  }

  top.data.forEach((bucket, bucketIndex) => {
    if (bucket && typeof bucket === 'object') {
      for (const [key, value] of Object.entries(bucket)) {
        if (Array.isArray(value)) {
          value.forEach(item => {
            const suffix = stableIdForItem(key, item);
            docs.push({
              _id: `${key}:${suffix}`,
              type: key,
              ...item,
            });
          });
        } else if (value && typeof value === 'object') {
          // single object in bucket
          docs.push({
            _id: `${key}:${bucketIndex + 1}`,
            type: key,
            ...value,
          });
        } else {
          // primitive -> meta
          docs.push({
            _id: `meta:${key}:${bucketIndex + 1}`,
            type: 'meta',
            value,
          });
        }
      }
    }
  });

  return docs;
}

/**
 * Conflict-aware bulk upsert. Retries per-doc conflicts with exponential backoff.
 */
async function upsertBulk(db, docs, batchSize = 1000, maxRetries = 3) {
  console.log(`upsertBulk: starting for ${docs.length} docs (batchSize=${batchSize})`);

  for (let i = 0; i < docs.length; i += batchSize) {
    let chunk = docs.slice(i, i + batchSize);
    const chunkIndex = Math.floor(i / batchSize);
    console.log(`  [chunk ${chunkIndex}] initial size ${chunk.length}`);

    // Helper: attach latest _rev if exists
    async function attachRevs(docArray) {
      const ids = docArray.map(d => d._id);
      const existing = await db.allDocs({ keys: ids, include_docs: true });
      const revMap = new Map();
      existing.rows.forEach(row => {
        if (row.doc && row.doc._rev) {
          revMap.set(row.key, row.doc._rev);
        }
      });
      return docArray.map(d => (revMap.has(d._id) ? { ...d, _rev: revMap.get(d._id) } : d));
    }

    // initial attach
    chunk = await attachRevs(chunk);

    let attempt = 0;
    while (attempt <= maxRetries) {
      attempt++;
      console.log(`    [chunk ${chunkIndex}] attempt ${attempt}`);
      let result;
      try {
        result = await db.bulkDocs(chunk);
      } catch (err) {
        console.error(`    [chunk ${chunkIndex}] bulkDocs failed entirely:`, err);
        break;
      }

      const toRetryIds = [];
      const unrecoverable = [];

      result.forEach((r, idx) => {
        if (r && r.error) {
          if (r.name === 'conflict' && attempt <= maxRetries) {
            toRetryIds.push(chunk[idx]._id);
          } else {
            unrecoverable.push({ error: r, doc: chunk[idx] });
          }
        }
      });

      if (toRetryIds.length === 0) {
        if (unrecoverable.length) {
          console.error(`    [chunk ${chunkIndex}] unrecoverable errors:`, unrecoverable.slice(0, 3));
        }
        break; // done with this chunk
      }

      // Prepare conflicted subset with fresh revs and retry
      console.log(`    [chunk ${chunkIndex}] retrying ${toRetryIds.length} conflicted docs`);
      const conflictedDocs = chunk.filter(d => toRetryIds.includes(d._id));
      const refreshed = await attachRevs(conflictedDocs);

      // Replace only conflicted docs in chunk for next attempt
      chunk = chunk.map(d => {
        const idx = toRetryIds.indexOf(d._id);
        return idx !== -1 ? refreshed[idx] : d;
      });

      // exponential backoff before next retry
      const backoffMs = 100 * Math.pow(2, attempt);
      await new Promise(res => setTimeout(res, backoffMs));
    }
  }

  console.log('upsertBulk: finished');
}

/**
 * Fetches all documents of type 'tblpatientcomplaints'.
 */
async function fetchAllComplaints(db) {
  // Make sure index exists for type
  await db.createIndex({ index: { fields: ['type'] } });

  const selector = { type: 'tblpatientcomplaints' };
  const limit = 50000; // adjust if you expect more; PouchDB/Mango doesn't paginate with bookmark easily

  const result = await db.find({
    selector,
    limit,
  });

  return result.docs;
}

/**
 * Builds an HTML table for the complaints and returns full page.
 */
function makeHtmlTable(complaints) {
  if (!complaints || complaints.length === 0) {
    return `<html><body><p>No tblpatientcomplaints found.</p></body></html>`;
  }

  // Collect all keys that appear across the docs for header consistency
  const headerSet = new Set();
  complaints.forEach(c => Object.keys(c).forEach(k => headerSet.add(k)));
  const headers = Array.from(headerSet).filter(h => h !== '_rev'); // omit _rev

  const rows = complaints.map(c =>
    headers.map(h => {
      const v = c[h];
      if (v === null || v === undefined) return '';
      return String(v).replace(/</g, '&lt;').replace(/>/g, '&gt;');
    })
  );

  const headerHtml = headers.map(h => `<th>${h}</th>`).join('');
  const bodyHtml = rows
    .map(
      cols =>
        `<tr>${cols.map(cell => `<td>${cell}</td>`).join('')}</tr>`
    )
    .join('\n');

  return `
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>tblpatientcomplaints</title>
  <style>
    body { font-family: system-ui,-apple-system,BlinkMacSystemFont,sans-serif; padding: 16px; }
    h1 { font-size: 1.5rem; }
    table { border-collapse: collapse; width: 100%; margin-top: 12px; }
    th, td { border: 1px solid #444; padding: 6px; text-align: left; font-size: 0.9rem; }
    th { background: #eee; position: sticky; top: 0; }
    tbody tr:nth-child(odd) { background: #f9f9f9; }
    tbody tr:hover { background: #ffe; }
    code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
  </style>
</head>
<body>
  <h1>All tblpatientcomplaints (${complaints.length})</h1>
  <table>
    <thead><tr>${headerHtml}</tr></thead>
    <tbody>${bodyHtml}</tbody>
  </table>
</body>
</html>`;
}

// ==== Entry point ==== //

process.on('unhandledRejection', e => {
  console.error('UnhandledRejection:', e);
});
process.on('uncaughtException', e => {
  console.error('UncaughtException:', e);
});

async function main() {
  console.log('>>> script started');

  let raw;
  try {
    raw = JSON.parse(fs.readFileSync('RemediNovaAPI.json', 'utf8'));
    console.log('Loaded JSON top-level keys:', Object.keys(raw));
  } catch (err) {
    console.error('Failed to read/parse RemediNovaAPI.json:', err);
    process.exit(1);
  }

  let docs;
  try {
    docs = jsonToDocs(raw);
    console.log(`Prepared ${docs.length} docs`);
  } catch (err) {
    console.error('Error converting JSON to docs:', err);
    process.exit(1);
  }

  try {
    const db = new PouchDB('remedinova');
    await upsertBulk(db, docs);
    console.log('Upsert complete.');

    // Create helpful indexes (idempotent)
    await db.createIndex({ index: { fields: ['type', 'ComplaintName'] } });
    await db.createIndex({ index: { fields: ['type', 'ReMeDi_Code'] } });
    console.log('Indexes ensured.');

    // Fetch all complaints
    const complaints = await fetchAllComplaints(db);
    console.log(`Fetched ${complaints.length} tblpatientcomplaints`);

    // Show summary in console (common fields)
    const summary = complaints.map(c => ({
      _id: c._id,
      ComplaintName: c.ComplaintName,
      ReMeDi_Code: c.ReMeDi_Code,
      IsDeleted: c.IsDeleted,
      IsEnabled: c.IsEnabled,
      domain: c.domain,
    }));
    console.table(summary);

    // Write full HTML
    const html = makeHtmlTable(complaints);
    fs.writeFileSync('tblpatientcomplaints.html', html, 'utf8');
    console.log(`Wrote HTML table to tblpatientcomplaints.html (${complaints.length} rows)`);
  } catch (err) {
    console.error('Fatal error in main processing:', err);
    process.exit(1);
  }
}

main();
