<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>RemediNova Data Tables</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="loadAllTables()" [disabled]="isInitialLoading">
        <ion-icon name="download-outline"></ion-icon>
      </ion-button>
      <ion-button (click)="initializeTables()" [disabled]="isInitialLoading">
        <ion-icon name="refresh-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Data Tables</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading Indicator -->
  <div *ngIf="isInitialLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading database information...</p>
  </div>

  <div *ngIf="!isInitialLoading">
    <!-- Summary Statistics -->
    <ion-card class="stats-card">
      <ion-card-header>
        <ion-card-title>Database Overview</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col size="3">
              <div class="stat-item">
                <h2>{{ tables.length }}</h2>
                <p>Total Tables</p>
              </div>
            </ion-col>
            <ion-col size="3">
              <div class="stat-item">
                <h2>{{ totalDocuments | number }}</h2>
                <p>Total Records</p>
              </div>
            </ion-col>
            <ion-col size="3">
              <div class="stat-item">
                <h2>{{ getLoadedTablesCount() }}</h2>
                <p>Loaded Tables</p>
              </div>
            </ion-col>
            <ion-col size="3">
              <div class="stat-item">
                <h2>{{ getTotalLoadedRecords() | number }}</h2>
                <p>Loaded Records</p>
              </div>
            </ion-col>
          </ion-row>
        </ion-grid>
        
        <ion-button 
          (click)="loadAllTables()" 
          expand="block" 
          fill="outline" 
          class="load-all-btn">
          <ion-icon name="download-outline" slot="start"></ion-icon>
          Load All Tables Data
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Tables List -->
    <div class="tables-container">
      <ion-card *ngFor="let table of tables; trackBy: trackByTableName" class="table-card">
        
        <!-- Table Header -->
        <ion-card-header (click)="toggleTable(table)" class="table-header clickable">
          <div class="table-header-content">
            <div class="table-info">
              <ion-icon [name]="getTableIcon(table.name)" class="table-icon"></ion-icon>
              <div class="table-details">
                <ion-card-title>{{ table.displayName }}</ion-card-title>
                <ion-card-subtitle>
                  {{ table.name }} • {{ table.totalCount | number }} records
                  <span *ngIf="table.data.length > 0" class="loaded-indicator">• Loaded</span>
                </ion-card-subtitle>
              </div>
            </div>
            <div class="table-actions">
              <ion-spinner *ngIf="table.isLoading" name="crescent" class="small-spinner"></ion-spinner>
              <ion-icon 
                *ngIf="!table.isLoading" 
                [name]="table.isExpanded ? 'chevron-up-outline' : 'chevron-down-outline'"
                class="expand-icon">
              </ion-icon>
            </div>
          </div>
        </ion-card-header>

        <!-- Table Content -->
        <ion-card-content *ngIf="table.isExpanded && !table.isLoading" class="table-content">
          
          <!-- Search Bar -->
          <div class="search-container" *ngIf="table.data.length > 0">
            <ion-searchbar
              [(ngModel)]="table.searchText"
              (ionInput)="onTableSearch(table)"
              [placeholder]="'Search in ' + table.displayName + '...'"
              debounce="300">
            </ion-searchbar>
            
            <div class="table-controls">
              <ion-button 
                (click)="exportTableData(table)" 
                fill="outline" 
                size="small">
                <ion-icon name="download-outline" slot="start"></ion-icon>
                Export JSON
              </ion-button>
              <span class="record-count">
                {{ table.filteredData.length }} / {{ table.data.length }} records
              </span>
            </div>
          </div>

          <!-- Data Table -->
          <div class="table-wrapper" *ngIf="table.filteredData.length > 0">
            <table class="data-table">
              <thead>
                <tr>
                  <th>#</th>
                  <th *ngFor="let column of table.columns">{{ column }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let record of table.filteredData.slice(0, 100); let i = index">
                  <td class="row-number">{{ i + 1 }}</td>
                  <td *ngFor="let column of table.columns" [title]="getDisplayValue(record[column])">
                    {{ getDisplayValue(record[column]) }}
                  </td>
                </tr>
              </tbody>
            </table>
            
            <div *ngIf="table.filteredData.length > 100" class="pagination-info">
              <ion-note>
                Showing first 100 records of {{ table.filteredData.length | number }}. 
                Use search to filter or export for complete data.
              </ion-note>
            </div>
          </div>

          <!-- No Data Message -->
          <div *ngIf="table.data.length === 0" class="no-data">
            <ion-icon name="document-outline" class="no-data-icon"></ion-icon>
            <p>No data available in this table</p>
          </div>

          <!-- No Search Results -->
          <div *ngIf="table.data.length > 0 && table.filteredData.length === 0" class="no-results">
            <ion-icon name="search-outline" class="no-data-icon"></ion-icon>
            <p>No records found for "{{ table.searchText }}"</p>
            <ion-button (click)="table.searchText = ''; onTableSearch(table)" fill="clear">
              Clear Search
            </ion-button>
          </div>

        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>
