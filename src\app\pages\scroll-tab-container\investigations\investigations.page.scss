// ------------------ investi tab
.diagnosis-card {
  // padding: 16px;
}

ion-card-content {
  // margin-top: 30px;
}

.diagnosis-header {
  margin-top: 16px;
  padding: 5px;
  height: 17px;
  display: block;
  font-size: 16px;
    margin-bottom: 30px;
    font-weight: 400;
  font-family: "DM Sans", sans-serif;

}

.select-diagnosis {

  display: flex;
  width: 1052px;
  justify-content: space-between;

}

.item-inner {
  border: none !important;
}

.select-diagnosis span {

  font-size: 14px;
  width: 100%;
  margin-top: 16px;
  color: #111827;
}

.dig-input-contain {
  display: flex;
}

.dig-input-contain span {
  width: 98px;
  padding: 9px;
  margin-left: 25px;
  font-size: 17px;
  color: #007AFF;
  font-weight: 600;
}

.diagnosis-input {
  width: 1052px;
  background: transparent;
  border: 1px solid #D1D5DB;
  border-radius: 9px;
  font-size: 12px;
  font-weight: 500;
  font-family: "DM Sans", sans-serif;
  color: #4A4A48;
  padding: 0px 8px 0 8px;
}

.diagnosis-segment {
  margin-bottom: 16px;
}

.diagnosis-input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  margin-top: 39px;
}

.diagnosis-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  justify-content: space-between;
}
.diagnosis-checkbox label{
 font-size: 16px;
  font-weight: 400;
  font-family: "DM Sans", sans-serif;

}

/* New Table Styles */
.lab-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  font-family: 'Inter', Arial, sans-serif;
}

.lab-table th,
.lab-table td {
  text-align: left;
  font-size: 14px;
}

.lab-table th {
  background: #D6E9FF;
  font-weight: 400;
  color: #374151;
  font-size: 12px;
  height: 33px;
}

.lab-table tr {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.lab-table tr:last-child {
  border-bottom: none;
}

.lab-table td {
  vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-right: 24px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  transition: color 0.2s;
}

.action-icons .icon:hover {
  color: #1976d2;
}

@media (max-width: 600px) {

  .lab-table th,
  .lab-table td {
    padding: 7px 4px;
    font-size: 12px;
  }
}

.lab-table th:nth-child(1),
.lab-table td:nth-child(1) {
  width: 535.5px;
  padding-left: 4px;
}

.lab-table th:nth-child(2),
.lab-table td:nth-child(2) {
  width: 535.5px;
}

.lab-table th:nth-child(3),
.lab-table td:nth-child(3) {
  width: 80px;
}

.lab-table th:nth-child(4),
.lab-table td:nth-child(4) {
  width: 58px;
}

.lab-table tr:nth-child(even) {
  background-color: #F9FAFB;
}
