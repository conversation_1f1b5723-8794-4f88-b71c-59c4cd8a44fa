 
 /* Content Area */
 .main-content{
  display: flex;
 }
        .content-area {
          margin-left: 20px;
            flex: 1;
            padding: 24px;
            background-color: #ffffff;
            overflow-y: auto;
        }

        /* Patient Info Card */
        .patient-info-card {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .patient-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 14px;
        }

        .patient-title {
            font-size: 20px;
            font-weight: 500;
            color: #111827;
        }

        .patient-details {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .patient-avatar {
            width: 64px;
            height: 64px;
            border-radius: 32px;
            background-color: #ffffff;
        }

        .patient-info {
            flex: 1;
        }

        .patient-name {
            font-size: 16px;
            font-weight: 400;
            color: #374151;
            margin-bottom: 4px;
        }

        .patient-gender {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }

        .patient-age {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .patient-age .unit {
            color: #9ca3af;
        }

        .patient-actions {
            display: flex;
            flex-direction: column;
            gap: 16px;
            align-items: flex-end;
        }

        .attended-by {
            text-align: right;
        }

        .attended-label {
            font-size: 12px;
            color: #9ca3af;
            margin-bottom: 4px;
        }

        .attended-name {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .view-readings {
            font-size: 14px;
            font-weight: 600;
            color: #007aff;
            text-decoration: none;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            align-items: center;
            margin-top: 56px;
        }

        .follow-up-checkbox {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 14px 16px;
        }

        .checkbox {
            width: 20px;
            height: 20px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
        }

        .btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-outline {
            border: 1px solid #007aff;
            color: #007aff;
            background-color: transparent;
        }

        .btn-outline:hover {
            background-color: #007aff;
            color: #ffffff;
        }

        .btn-primary {
            background-color: #007aff;
            color: #ffffff;
            border: none;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }
