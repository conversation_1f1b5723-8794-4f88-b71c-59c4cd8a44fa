import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';



@Injectable({ providedIn: 'root' })
export class PouchdbService {
  private db!: PouchDB.Database;
  private remoteDB!: PouchDB.Database;

  constructor() {
    PouchDB.plugin(PouchDBFind);
    this.initDB('local_consultan'); // ✅ Change DB name if required
  }

  /** ✅ Initialize Local PouchDB */
  private initDB(name: string) {
    this.db = new PouchDB(name, { adapter: 'idb' });

  }



  // 🔹================ MASTER DATA FUNCTIONS =================

  /** ✅ Store or Update Entire Master Data JSON */
  addOrUpdateMasterData(data: any): Observable<any> {
    const docId = 'master_data';

    return from(
      this.db.get(docId).then(doc =>
        this.db.put({ ...doc, ...data, _id: docId, _rev: doc._rev })
      ).catch(() =>
        this.db.put({ _id: docId, ...data })
      )
    ).pipe(map(res => res), catchError(this.handleError));
  }

  /** ✅ Fetch Entire Master Data JSON */
  getMasterData(): Observable<any> {
    return from(this.db.get('master_data')).pipe(map(res => res), catchError(this.handleError));
  }

  /** ✅ Fetch Specific Table (Example: countries, states, etc.) */
  getMasterTable(tableName: string): Observable<any[]> {
    return from(this.db.get('master_data')).pipe(
      map((res: any) => res[tableName] || []),
      catchError(this.handleError)
    );
  }

  // 🔹================ SYNC FUNCTIONS =================



  // 🔹 Common Error Handler
  private handleError(error: any) {
    console.error('❌ PouchDB Error:', error);
    return throwError(() =>error);
  }
}
