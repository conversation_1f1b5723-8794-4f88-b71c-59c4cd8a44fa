import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import { Observable, from, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export interface PatientHistoryPage {
  _id?: string;
  historyOfPresentIllness: string;
  personalHistory: string;
  pastMedicalOrSurgicalHistory: string;
  familyHistory: string;
  currentAndRecentMedications: string;
  medicalAllergies: string;
  otherAllergiesOrSensitivities: string;
  additionalNotes: string;
  physicalExamination: string;
  reviewNote: string;
  createdAt?: string;
  type?: string;
}

// The exact shape stored in PouchDB (metadata + required fields)
export interface StoredPatientHistory extends PatientHistoryPage {
  _id: string; // required here
  _rev?: string;
  type: 'patient-history';
  createdAt: string;
}

@Injectable({ providedIn: 'root' })
export class SaveService {
  private db: PouchDB.Database;

  constructor() {
    this.db = new PouchDB('local-consultan', { adapter: 'idb' });
  }

  savePatientHistory(entry: PatientHistory): Observable<PouchDB.Core.Response> {
    const now = new Date().toISOString();
    const doc: StoredPatientHistory = {
      ...entry,
      createdAt: entry.createdAt || now,
      type: 'patient-history',
      _id: entry._id || `patient-history:${Date.now()}`,
    } as StoredPatientHistory;

    return from(this.db.put(doc as any)).pipe(
      catchError((e) => throwError(() => this.normalizeError(e)))
    );
  }

  getAllPatientHistories(): Observable<PatientHistory[]> {
    return from(
      this.db.allDocs<StoredPatientHistory>({ include_docs: true, descending: false })
    ).pipe(
      map((res) => {
        // type guard
        const isStored = (d: any): d is StoredPatientHistory =>
          !!d && d.type === 'patient-history';

        const filtered: StoredPatientHistory[] = res.rows
          .map((r) => r.doc)
          .filter(isStored);

        // strip internal metadata if desired (keep rest)
        return filtered.map(({ _id, _rev, ...rest }) => rest as PatientHistory);
      }),
      catchError((e) => throwError(() => this.normalizeError(e)))
    );
  }

  private normalizeError(err: any): Error {
    if (err && typeof err === 'object') {
      if (err.reason) return new Error(err.reason);
      if (err.message) return new Error(err.message);
    }
    return new Error(JSON.stringify(err));
  }
}
