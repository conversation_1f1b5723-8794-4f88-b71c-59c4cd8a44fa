import { Injectable } from '@angular/core';
import { RemedinovaDataService } from './remedinova-data.service';

@Injectable({
  providedIn: 'root'
})
export class DataLoaderService {
  private isLoaded = false;
  private loadingPromise: Promise<void> | null = null;

  constructor(private remedinovaData: RemedinovaDataService) {}

  /**
   * Ensures data is loaded only once across the application
   */
  async ensureDataLoaded(): Promise<void> {
    if (this.isLoaded) {
      return;
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = this.loadData();
    await this.loadingPromise;
    this.isLoaded = true;
  }

  private async loadData(): Promise<void> {
    console.log('Loading RemediNova data...');
    const startTime = Date.now();
    
    try {
      await this.remedinovaData.initialize();
      const endTime = Date.now();
      console.log(`RemediNova data loaded successfully in ${endTime - startTime}ms`);
      
      // Log database statistics
      const stats = await this.remedinovaData.getDatabaseStats();
      console.log('Database Statistics:', stats);
      
    } catch (error) {
      console.error('Failed to load RemediNova data:', error);
      this.loadingPromise = null;
      throw error;
    }
  }

  /**
   * Check if data is loaded
   */
  isDataLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * Force reload all data
   */
  async reloadData(): Promise<void> {
    this.isLoaded = false;
    this.loadingPromise = null;
    await this.remedinovaData.reinitializeData();
    await this.ensureDataLoaded();
  }
}
