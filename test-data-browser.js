const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    },
    show: true
  });

  mainWindow.webContents.openDevTools();
  
  mainWindow.loadFile(path.join(__dirname, 'www/index.html'));

  mainWindow.webContents.on('dom-ready', () => {
    console.log('DOM ready, checking data browser...');
    
    // Wait for Angular to load, then test data browser
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        console.log('=== TESTING DATA BROWSER ===');
        
        // Check for table browser component
        const tableBrowser = document.querySelector('app-table-browser');
        console.log('Table browser component found:', !!tableBrowser);
        
        // Check for loading spinner
        const spinner = document.querySelector('ion-spinner');
        console.log('Loading spinner found:', !!spinner);
        
        // Check for table select
        const tableSelect = document.querySelector('ion-select');
        console.log('Table select found:', !!tableSelect);
        
        // Check for search bar
        const searchBar = document.querySelector('ion-searchbar');
        console.log('Search bar found:', !!searchBar);
        
        // Check for cards
        const cards = document.querySelectorAll('ion-card');
        console.log('Found cards:', cards.length);
        
        // Check for PouchDB
        console.log('PouchDB available:', typeof PouchDB !== 'undefined');
        
        // Test PouchDB functionality
        if (typeof PouchDB !== 'undefined') {
          try {
            const testDB = new PouchDB('test-db');
            console.log('PouchDB test database created successfully');
            testDB.destroy().then(() => {
              console.log('PouchDB test database destroyed successfully');
            });
          } catch (error) {
            console.error('PouchDB test failed:', error);
          }
        }
        
        console.log('=== END DATA BROWSER TEST ===');
      `);
    }, 3000);
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
