const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Building Ionic Angular app for Electron...');

try {
  // Build the Angular app
  console.log('1. Building Angular app...');
  execSync('ng build --configuration production', { stdio: 'inherit' });

  // Copy electron files
  console.log('2. Copying Electron files...');
  if (!fs.existsSync('www')) {
    fs.mkdirSync('www');
  }

  // Copy main.js to www directory
  fs.copyFileSync('main.js', 'www/main.js');

  // Create package.json for Electron in www directory
  const electronPackageJson = {
    "name": "uiPart-electron",
    "version": "1.0.0",
    "main": "main.js",
    "scripts": {
      "start": "electron ."
    },
    "devDependencies": {
      "electron": "^37.2.4"
    }
  };

  fs.writeFileSync('www/package.json', JSON.stringify(electronPackageJson, null, 2));

  console.log('3. Build completed successfully!');
  console.log('To run the Electron app:');
  console.log('  cd www');
  console.log('  npm install');
  console.log('  npm start');

} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
}
