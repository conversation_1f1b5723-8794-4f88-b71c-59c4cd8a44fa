// Electron-specific scroll fixes
(function() {
  'use strict';

  // Check if running in Electron
  const isElectron = () => {
    return typeof window !== 'undefined' &&
           window.process &&
           window.process.type === 'renderer';
  };

  // Apply Electron-specific fixes when DOM is ready
  const applyElectronFixes = () => {
    if (!isElectron()) return;

    console.log('Applying Electron scroll fixes...');

    // Force proper overflow settings on ion-content elements
    const fixIonContent = () => {
      const ionContents = document.querySelectorAll('ion-content');
      ionContents.forEach(content => {
        const scrollElement = content.shadowRoot?.querySelector('.inner-scroll') || content;
        if (scrollElement) {
          scrollElement.style.overflowY = 'auto';
          scrollElement.style.overflowX = 'auto';
          scrollElement.style.webkitOverflowScrolling = 'touch';
          scrollElement.style.height = '100%';
        }
      });
    };

    // Fix scrollable segments and tabs
    const fixScrollableSegments = () => {
      // Fix ion-segment elements
      const segments = document.querySelectorAll('ion-segment[scrollable]');
      segments.forEach(segment => {
        segment.style.overflowX = 'auto';
        segment.style.overflowY = 'hidden';
        segment.style.whiteSpace = 'nowrap';
        segment.style.webkitOverflowScrolling = 'touch';
        segment.style.display = 'inline-flex';
        segment.style.flexWrap = 'nowrap';
        segment.style.minWidth = 'max-content';
        segment.style.width = 'auto';
        segment.style.opacity = '1';
        segment.style.visibility = 'visible';
        segment.style.position = 'relative';
      });

      // Fix scroll tabs wrapper
      const tabWrappers = document.querySelectorAll('.scroll-tabs-wrapper');
      tabWrappers.forEach(wrapper => {
        wrapper.style.overflowX = 'auto';
        wrapper.style.overflowY = 'hidden';
        wrapper.style.whiteSpace = 'nowrap';
        wrapper.style.webkitOverflowScrolling = 'touch';
        wrapper.style.display = 'block';
        wrapper.style.width = '100%';
        wrapper.style.opacity = '1';
        wrapper.style.visibility = 'visible';
      });

      // Fix ion-segment-button elements
      const segmentButtons = document.querySelectorAll('ion-segment-button');
      segmentButtons.forEach(button => {
        button.style.display = 'inline-block';
        button.style.opacity = '1';
        button.style.visibility = 'visible';
        button.style.flexShrink = '0';
        button.style.whiteSpace = 'nowrap';
        button.style.minWidth = '158px';
        button.style.position = 'relative';
        button.style.color = '#333';
        button.style.fontSize = '16px';
        button.style.fontWeight = '400';
        button.style.background = 'transparent';

        // Fix button native element
        const buttonNative = button.querySelector('.button-native');
        if (buttonNative) {
          buttonNative.style.color = '#333';
          buttonNative.style.opacity = '1';
          buttonNative.style.visibility = 'visible';
          buttonNative.style.fontSize = '16px';
          buttonNative.style.fontWeight = '400';
          buttonNative.style.display = 'block';
        }
      });
    };

    // Fix main content areas
    const fixMainContent = () => {
      const mainContents = document.querySelectorAll('.main-content, .dashboard-grid, .scroll-tabs-wrapper, .tab-content');
      mainContents.forEach(element => {
        element.style.overflowY = 'auto';
        element.style.overflowX = 'auto';
        element.style.webkitOverflowScrolling = 'touch';
        element.style.scrollBehavior = 'smooth';
      });
    };

    // Apply fixes immediately
    fixIonContent();
    fixScrollableSegments();
    fixMainContent();

    // Add tab switching support
    const addTabSwitchingSupport = () => {
      const segment = document.querySelector('ion-segment');
      if (segment) {
        segment.addEventListener('ionChange', (event) => {
          console.log('Tab change detected in scroll fix:', event.detail.value);

          // Force change detection and repaint
          setTimeout(() => {
            const tabContent = document.querySelector('.tab-content');
            if (tabContent) {
              tabContent.style.transform = 'translateZ(0)';
              setTimeout(() => {
                tabContent.style.transform = '';
              }, 10);
            }
          }, 50);
        });
      }
    };

    // Re-apply fixes when new elements are added (for dynamic content)
    const observer = new MutationObserver(() => {
      fixIonContent();
      fixScrollableSegments();
      fixMainContent();
      addTabSwitchingSupport();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Initial tab switching support
    addTabSwitchingSupport();

    // Force repaint to ensure styles are applied
    setTimeout(() => {
      document.body.style.display = 'none';
      document.body.offsetHeight; // Trigger reflow
      document.body.style.display = '';
    }, 100);
  };

  // Apply fixes when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyElectronFixes);
  } else {
    applyElectronFixes();
  }

  // Also apply when Angular/Ionic is fully loaded
  window.addEventListener('load', () => {
    setTimeout(applyElectronFixes, 500);
  });

})();
