import { Injectable } from '@angular/core';
import { PouchService } from './pouch.service';
import { RemedinovaDataService } from './remedinova-data.service';
import { DataLoaderService } from './data-loader.service';

@Injectable({
  providedIn: 'root'
})
export class DataTestService {

  constructor(
    private pouchService: PouchService,
    private remedinovaData: RemedinovaDataService,
    private dataLoader: DataLoaderService
  ) {}

  async runDiagnostics(): Promise<{
    success: boolean;
    errors: string[];
    warnings: string[];
    info: any;
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const info: any = {};

    try {
      console.log('🔍 Running RemediNova data diagnostics...');

      // Test 1: Check if JSON file is accessible
      try {
        const response = await fetch('assets/data/RemediNovaAPI.json');
        if (!response.ok) {
          errors.push(`Failed to fetch RemediNovaAPI.json: ${response.status} ${response.statusText}`);
        } else {
          const data = await response.json();
          info.jsonFileSize = JSON.stringify(data).length;
          info.hasDataArray = Array.isArray(data.data);
          info.dataArrayLength = data.data ? data.data.length : 0;
          console.log('✅ JSON file accessible');
        }
      } catch (error: any) {
        errors.push(`JSON file access error: ${error.message}`);
      }

      // Test 2: Check PouchDB initialization
      try {
        const availableTables = this.pouchService.getAvailableTables();
        info.configuredTables = availableTables.length;
        console.log(`✅ PouchDB configured with ${availableTables.length} tables`);
      } catch (error: any) {
        errors.push(`PouchDB initialization error: ${error.message}`);
      }

      // Test 3: Test data loading
      try {
        await this.dataLoader.ensureDataLoaded();
        console.log('✅ Data loader completed');
      } catch (error: any) {
        errors.push(`Data loading error: ${error.message}`);
      }

      // Test 4: Check database statistics
      try {
        const stats = await this.remedinovaData.getDatabaseStats();
        info.loadedTables = Object.keys(stats).length;
        info.totalRecords = Object.values(stats).reduce(
          (total: number, stat: any) => total + (stat.doc_count || 0), 
          0
        );
        console.log(`✅ Database stats: ${info.loadedTables} tables, ${info.totalRecords} records`);
      } catch (error: any) {
        errors.push(`Database stats error: ${error.message}`);
      }

      // Test 5: Test sample data retrieval
      try {
        const complaints = await this.remedinovaData.getAllComplaints();
        info.sampleComplaintsCount = complaints.length;
        
        const medicines = await this.remedinovaData.getAllMedicines();
        info.sampleMedicinesCount = medicines.length;
        
        console.log(`✅ Sample data: ${complaints.length} complaints, ${medicines.length} medicines`);
      } catch (error: any) {
        warnings.push(`Sample data retrieval warning: ${error.message}`);
      }

      // Test 6: Test search functionality
      try {
        const searchResults = await this.remedinovaData.searchComplaints('fever');
        info.searchResultsCount = searchResults.length;
        console.log(`✅ Search test: found ${searchResults.length} results for 'fever'`);
      } catch (error: any) {
        warnings.push(`Search functionality warning: ${error.message}`);
      }

      const success = errors.length === 0;
      
      if (success) {
        console.log('🎉 All diagnostics passed!');
      } else {
        console.error('❌ Diagnostics failed with errors:', errors);
      }

      return {
        success,
        errors,
        warnings,
        info
      };

    } catch (error: any) {
      errors.push(`Diagnostic test failed: ${error.message}`);
      return {
        success: false,
        errors,
        warnings,
        info
      };
    }
  }

  async quickTest(): Promise<boolean> {
    try {
      console.log('🚀 Running quick test...');
      
      // Quick test: just try to load data and get basic stats
      await this.dataLoader.ensureDataLoaded();
      const stats = await this.remedinovaData.getDatabaseStats();
      const totalRecords = Object.values(stats).reduce(
        (total: number, stat: any) => total + (stat.doc_count || 0), 
        0
      );
      
      console.log(`✅ Quick test passed: ${Object.keys(stats).length} tables, ${totalRecords} records`);
      return true;
      
    } catch (error: any) {
      console.error('❌ Quick test failed:', error);
      return false;
    }
  }

  async clearAllData(): Promise<void> {
    try {
      console.log('🗑️ Clearing all data...');
      await this.remedinovaData.clearAllData();
      console.log('✅ All data cleared');
    } catch (error: any) {
      console.error('❌ Failed to clear data:', error);
      throw error;
    }
  }

  async resetAndReload(): Promise<void> {
    try {
      console.log('🔄 Resetting and reloading data...');
      await this.clearAllData();
      await this.dataLoader.reloadData();
      console.log('✅ Data reset and reloaded');
    } catch (error: any) {
      console.error('❌ Failed to reset and reload:', error);
      throw error;
    }
  }
}
