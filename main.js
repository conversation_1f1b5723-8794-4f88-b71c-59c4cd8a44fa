const { app, BrowserWindow, nativeTheme } = require('electron');
const path = require('path');
const fs = require('fs');

// Set light theme for Electron
nativeTheme.themeSource = 'light';

function createWindow() {
  console.log('Creating Electron window...');

  const mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false,
      allowRunningInsecureContent: true,
      devTools: true
    },
    show: false, // Don't show until ready
    backgroundColor: '#ffffff',
    titleBarStyle: 'default'
  });

  console.log('Window created, loading content...');

  // Check if index.html exists
  const indexPath = path.join(__dirname, 'www/index.html');
  console.log('Looking for index.html at:', indexPath);

  if (!fs.existsSync(indexPath)) {
    console.error('index.html not found at:', indexPath);
    console.log('Please run: npm run build first');
    return;
  }

  // Load the Angular/Ionic built index.html
  mainWindow.loadFile(indexPath).catch(err => {
    console.error('Failed to load index.html:', err);
  });

  // Show window when ready and inject fixes
  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show');

    // Inject critical CSS immediately
    mainWindow.webContents.insertCSS(`
      /* Critical fixes for Electron */
      html, body {
        height: 100vh !important;
        width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
      }

      app-root {
        display: block !important;
        height: 100vh !important;
        width: 100vw !important;
      }

      ion-app {
        height: 100vh !important;
        width: 100vw !important;
        display: block !important;
      }

      ion-content {
        height: 100% !important;
        overflow-y: auto !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
      }
    `);

    mainWindow.show();
    console.log('Window shown successfully');
  });

  // Handle loading errors
  mainWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription) => {
    console.error('Failed to load page:', errorCode, errorDescription);
  });

  // Handle page loading events
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Page finished loading');
  });

  // Inject CSS fixes after DOM is ready
  mainWindow.webContents.on('dom-ready', () => {
    console.log('DOM ready, checking for Angular app...');

    // Check if Angular app is loaded
    mainWindow.webContents.executeJavaScript(`
      console.log('Checking Angular app status...');
      console.log('Document body:', document.body.innerHTML.substring(0, 200));
      console.log('app-root element:', document.querySelector('app-root'));

      // Check for any JavaScript errors
      window.addEventListener('error', (e) => {
        console.error('JavaScript error:', e.error);
      });

      // Check if Angular is loaded
      setTimeout(() => {
        const appRoot = document.querySelector('app-root');
        if (appRoot) {
          console.log('Angular app-root found');
          console.log('app-root content:', appRoot.innerHTML.substring(0, 200));
        } else {
          console.error('Angular app-root not found!');
        }

        // Check for ion-app
        const ionApp = document.querySelector('ion-app');
        if (ionApp) {
          console.log('ion-app found');
        } else {
          console.error('ion-app not found!');
        }
      }, 2000);
    `);

    // Inject CSS fixes for better scrolling
    mainWindow.webContents.insertCSS(`
      /* Electron-specific scroll fixes */
      html, body {
        overflow: hidden !important;
        height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      ion-app {
        height: 100vh !important;
        overflow: hidden !important;
        display: block !important;
      }

      ion-content {
        overflow-y: auto !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        height: 100% !important;
        display: block !important;
      }

      .main-content,
      .dashboard-grid,
      .tab-content {
        overflow-y: auto !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
      }

      /* Specific fixes for horizontal scrolling tabs */
      .scroll-tabs-wrapper {
        overflow-x: auto !important;
        overflow-y: hidden !important;
        white-space: nowrap !important;
        -webkit-overflow-scrolling: touch !important;
        display: block !important;
        width: 100% !important;
        opacity: 1 !important;
        visibility: visible !important;
      }

      .scroll-tabs-wrapper::-webkit-scrollbar {
        height: 8px !important;
        display: block !important;
      }

      .scroll-tabs-wrapper::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 4px !important;
      }

      .scroll-tabs-wrapper::-webkit-scrollbar-thumb {
        background: #c1c1c1 !important;
        border-radius: 4px !important;
      }

      ion-segment[scrollable] {
        overflow-x: auto !important;
        overflow-y: hidden !important;
        white-space: nowrap !important;
        display: inline-flex !important;
        flex-wrap: nowrap !important;
        min-width: max-content !important;
        width: auto !important;
        opacity: 1 !important;
        visibility: visible !important;
      }

      ion-segment-button {
        display: inline-block !important;
        opacity: 1 !important;
        visibility: visible !important;
        flex-shrink: 0 !important;
        white-space: nowrap !important;
        min-width: 158px !important;
      }

      /* Ensure app-root is visible */
      app-root {
        display: block !important;
        height: 100vh !important;
        width: 100vw !important;
      }

      /* Ensure Ionic components render properly in Electron */
      ion-card {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        background: white !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        margin: 16px !important;
        overflow: hidden !important;
      }

      ion-button {
        display: inline-block !important;
        opacity: 1 !important;
        visibility: visible !important;
      }

      ion-input {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
      }

      ion-checkbox {
        display: inline-block !important;
        opacity: 1 !important;
        visibility: visible !important;
      }

      ion-list {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
      }

      ion-item {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
      }

      /* Fix for all Ionic components */
      [class*="ion-"] {
        opacity: 1 !important;
        visibility: visible !important;
      }
    `);

    // Add scroll behavior fixes after Angular loads
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        console.log('Applying JavaScript scroll fixes...');

        // Fix ion-content scrolling
        const ionContents = document.querySelectorAll('ion-content');
        console.log('Found ion-content elements:', ionContents.length);

        ionContents.forEach(content => {
          const scrollElement = content.shadowRoot?.querySelector('.inner-scroll') || content;
          if (scrollElement) {
            scrollElement.style.overflowY = 'auto';
            scrollElement.style.overflowX = 'auto';
            scrollElement.style.webkitOverflowScrolling = 'touch';
            scrollElement.style.height = '100%';
          }
        });

        // Fix main content areas
        const mainContents = document.querySelectorAll('.main-content, .dashboard-grid');
        console.log('Found main content elements:', mainContents.length);

        mainContents.forEach(element => {
          element.style.overflowY = 'auto';
          element.style.overflowX = 'auto';
          element.style.webkitOverflowScrolling = 'touch';
        });

        // Fix horizontal scrolling tabs
        const tabWrappers = document.querySelectorAll('.scroll-tabs-wrapper');
        console.log('Found tab wrapper elements:', tabWrappers.length);

        tabWrappers.forEach(wrapper => {
          wrapper.style.overflowX = 'auto';
          wrapper.style.overflowY = 'hidden';
          wrapper.style.whiteSpace = 'nowrap';
          wrapper.style.webkitOverflowScrolling = 'touch';
          wrapper.style.display = 'block';
          wrapper.style.width = '100%';
          wrapper.style.opacity = '1';
          wrapper.style.visibility = 'visible';
        });

        const segments = document.querySelectorAll('ion-segment[scrollable]');
        console.log('Found scrollable segments:', segments.length);

        segments.forEach(segment => {
          segment.style.overflowX = 'auto';
          segment.style.overflowY = 'hidden';
          segment.style.whiteSpace = 'nowrap';
          segment.style.display = 'inline-flex';
          segment.style.flexWrap = 'nowrap';
          segment.style.minWidth = 'max-content';
          segment.style.width = 'auto';
          segment.style.opacity = '1';
          segment.style.visibility = 'visible';
        });

        const segmentButtons = document.querySelectorAll('ion-segment-button');
        console.log('Found segment buttons:', segmentButtons.length);

        segmentButtons.forEach(button => {
          button.style.display = 'inline-block';
          button.style.opacity = '1';
          button.style.visibility = 'visible';
          button.style.flexShrink = '0';
          button.style.whiteSpace = 'nowrap';
          button.style.minWidth = '158px';
          button.style.color = '#333';
          button.style.fontSize = '16px';
          button.style.fontWeight = '400';
          button.style.background = 'transparent';

          // Fix button native element
          const buttonNative = button.querySelector('.button-native');
          if (buttonNative) {
            buttonNative.style.color = '#333';
            buttonNative.style.opacity = '1';
            buttonNative.style.visibility = 'visible';
            buttonNative.style.fontSize = '16px';
            buttonNative.style.fontWeight = '400';
            buttonNative.style.display = 'block';
          }
        });

        // Add tab switching support for Electron
        setTimeout(() => {
          const tabsWithContent = document.querySelectorAll('ion-segment-button');
          console.log('Checking tab content...');
          tabsWithContent.forEach((button, index) => {
            console.log('Tab', index, 'text content:', button.textContent.trim());

            // Add click listener for manual tab switching
            button.addEventListener('click', () => {
              console.log('Manual tab click detected:', button.textContent.trim());

              // Force Angular change detection
              setTimeout(() => {
                const event = new CustomEvent('ionChange', {
                  detail: { value: button.getAttribute('value') }
                });
                const segment = document.querySelector('ion-segment');
                if (segment) {
                  segment.dispatchEvent(event);
                }
              }, 50);
            });
          });

          // Monitor for tab content changes
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              if (mutation.type === 'attributes' && mutation.attributeName === 'data-selected-tab') {
                console.log('Tab content updated:', mutation.target.getAttribute('data-selected-tab'));

                // Force repaint
                const tabContent = mutation.target;
                tabContent.style.transform = 'translateZ(0)';
                setTimeout(() => {
                  tabContent.style.transform = '';
                }, 10);
              }
            });
          });

          const tabContent = document.querySelector('.tab-content');
          if (tabContent) {
            observer.observe(tabContent, { attributes: true });
          }
        }, 1000);

        // Fix Ionic components specifically
        const ionicComponents = document.querySelectorAll('ion-card, ion-button, ion-input, ion-checkbox, ion-list, ion-item');
        console.log('Found Ionic components:', ionicComponents.length);

        ionicComponents.forEach(component => {
          component.style.display = component.tagName.toLowerCase() === 'ion-card' ? 'block' :
                                   component.tagName.toLowerCase() === 'ion-button' ? 'inline-block' :
                                   component.tagName.toLowerCase() === 'ion-checkbox' ? 'inline-block' : 'block';
          component.style.opacity = '1';
          component.style.visibility = 'visible';

          // Special handling for ion-card
          if (component.tagName.toLowerCase() === 'ion-card') {
            component.style.background = 'white';
            component.style.borderRadius = '8px';
            component.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            component.style.margin = '16px';
            component.style.overflow = 'hidden';
          }
        });

        console.log('JavaScript fixes applied including horizontal tabs and Ionic components');
      `);
    }, 3000);
  });
}

app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});
