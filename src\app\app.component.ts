import { Component } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { NavbarPage } from './pages/navbar/navbar.page';
import { HomePage } from './home/<USER>';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [IonicModule, HomePage,],
  templateUrl: 'app.component.html',
})
export class AppComponent {
  constructor() {
    this.applyConstructedStyle();
  }

    applyConstructedStyle() {
    const sheet = new CSSStyleSheet();
    sheet.replaceSync(`
      :root {
        color-scheme: light;
      }

      body {
        background-color: white;
        color: black;
      }
        #background-content {
    
    background: #fffdfd;
}
    `);

    // Apply to the document (only works in Chromium-based browsers)
    if ('adoptedStyleSheets' in Document.prototype) {
      document.adoptedStyleSheets = [sheet];
    }
}
}

