import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RemedinovaDataService } from '../../services/remedinova-data.service';
import { DataLoaderService } from '../../services/data-loader.service';
import { PouchService } from '../../services/pouch.service';

interface TableSummary {
  name: string;
  displayName: string;
  count: number;
  sampleData: any[];
  isLoaded: boolean;
}

@Component({
  selector: 'app-all-tables-view',
  templateUrl: './all-tables-view.page.html',
  styleUrls: ['./all-tables-view.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class AllTablesViewPage implements OnInit {
  
  tableSummaries: TableSummary[] = [];
  isLoading = false;
  totalRecords = 0;
  loadedTables = 0;
  
  // Table display names
  private tableDisplayNames: { [key: string]: string } = {
    'locationhierarchy': 'Location Hierarchy',
    'tblpatientcomplaints': 'Patient Complaints',
    'tblstate': 'States',
    'tblcountry': 'Countries', 
    'tbldistrict': 'Districts',
    'tblblock': 'Blocks',
    'tblvillage': 'Villages',
    'tblspeciality_list': 'Medical Specialties',
    'tblreferralspecialtylist': 'Referral Specialties',
    'tblinstructions': 'Medical Instructions',
    'tbllabcategory': 'Lab Categories',
    'tbllabsubtest': 'Lab Sub Tests',
    'tblspecialinstruction': 'Special Instructions',
    'tblmedicinemaster': 'Medicine Master',
    'tblmedicationbrand': 'Medication Brands',
    'tbldrug_class': 'Drug Classes',
    'tbldrug_form': 'Drug Forms',
    'tbldiagnosiscategory': 'Diagnosis Categories',
    'tbldiagnosischapter': 'Diagnosis Chapters',
    'tbldiagnosismaster': 'Diagnosis Master',
    'tbldiagnosissubchapter': 'Diagnosis Sub Chapters'
  };

  constructor(
    private remedinovaData: RemedinovaDataService,
    private dataLoader: DataLoaderService,
    private pouchService: PouchService
  ) {}

  async ngOnInit() {
    await this.loadAllTablesData();
  }

  async loadAllTablesData() {
    this.isLoading = true;
    
    try {
      // Ensure data is loaded
      await this.dataLoader.ensureDataLoaded();
      
      // Get available tables
      const availableTables = this.pouchService.getAvailableTables();
      
      // Get database statistics
      const stats = await this.remedinovaData.getDatabaseStats();
      
      // Load sample data for each table
      const tablePromises = availableTables.map(async (config) => {
        try {
          const allData = await this.pouchService.getAllFromTable(config.name);
          const sampleData = allData.slice(0, 5); // First 5 records
          
          return {
            name: config.name,
            displayName: this.tableDisplayNames[config.name] || config.name,
            count: stats[config.name]?.doc_count || 0,
            sampleData: sampleData,
            isLoaded: true
          };
        } catch (error) {
          console.error(`Error loading ${config.name}:`, error);
          return {
            name: config.name,
            displayName: this.tableDisplayNames[config.name] || config.name,
            count: stats[config.name]?.doc_count || 0,
            sampleData: [],
            isLoaded: false
          };
        }
      });
      
      this.tableSummaries = await Promise.all(tablePromises);
      
      // Sort by record count (descending)
      this.tableSummaries.sort((a, b) => b.count - a.count);
      
      // Calculate totals
      this.totalRecords = this.tableSummaries.reduce((sum, table) => sum + table.count, 0);
      this.loadedTables = this.tableSummaries.filter(table => table.isLoaded).length;
      
    } catch (error) {
      console.error('Error loading tables data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  getTableIcon(tableName: string): string {
    const iconMap: { [key: string]: string } = {
      'tblpatientcomplaints': 'medical-outline',
      'tblmedicinemaster': 'flask-outline',
      'tbldiagnosismaster': 'clipboard-outline',
      'tblstate': 'location-outline',
      'tblcountry': 'globe-outline',
      'tbldistrict': 'map-outline',
      'tblvillage': 'home-outline',
      'tbllabsubtest': 'beaker-outline',
      'tblspeciality_list': 'school-outline',
      'tblinstructions': 'document-text-outline',
      'locationhierarchy': 'layers-outline',
      'tbllabcategory': 'library-outline',
      'tblspecialinstruction': 'bookmark-outline',
      'tblmedicationbrand': 'pricetag-outline',
      'tbldrug_class': 'albums-outline',
      'tbldrug_form': 'shapes-outline',
      'tbldiagnosiscategory': 'folder-outline',
      'tbldiagnosischapter': 'book-outline',
      'tbldiagnosissubchapter': 'bookmarks-outline',
      'tblreferralspecialtylist': 'people-outline',
      'tblblock': 'grid-outline'
    };
    return iconMap[tableName] || 'list-outline';
  }

  getDisplayValue(value: any): string {
    if (value === null || value === undefined) {
      return '-';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    const str = value.toString();
    return str.length > 50 ? str.substring(0, 50) + '...' : str;
  }

  getTableColumns(sampleData: any[]): string[] {
    if (sampleData.length === 0) return [];
    
    const allKeys = new Set<string>();
    sampleData.forEach(record => {
      Object.keys(record).forEach(key => {
        if (!key.startsWith('_')) { // Exclude PouchDB internal fields
          allKeys.add(key);
        }
      });
    });
    
    return Array.from(allKeys).sort().slice(0, 8); // Show max 8 columns
  }

  async exportAllData() {
    const exportData: { [tableName: string]: any[] } = {};
    
    for (const table of this.tableSummaries) {
      if (table.isLoaded) {
        try {
          const data = await this.pouchService.getAllFromTable(table.name);
          exportData[table.name] = data;
        } catch (error) {
          console.error(`Error exporting ${table.name}:`, error);
          exportData[table.name] = [];
        }
      }
    }
    
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'remedinova_all_tables_data.json';
    link.click();
    
    URL.revokeObjectURL(url);
  }

  async exportTableData(table: TableSummary) {
    try {
      const data = await this.pouchService.getAllFromTable(table.name);
      const dataStr = JSON.stringify(data, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${table.name}_data.json`;
      link.click();
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error(`Error exporting ${table.name}:`, error);
    }
  }

  getTableColorClass(index: number): string {
    const colors = ['primary', 'secondary', 'tertiary', 'success', 'warning', 'danger'];
    return colors[index % colors.length];
  }
}
