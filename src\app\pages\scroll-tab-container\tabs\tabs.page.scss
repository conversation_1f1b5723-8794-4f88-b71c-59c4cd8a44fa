
input,
select {
  border: none;
  outline: none;
  box-shadow: none;
  background: transparent; /* Optional: if you want transparent background */
}
button{
  background-color: transparent;
}
.ion-inherit-color {
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 700;
    color: #4A4A48;
    transform: rotate(0deg);
    gap: 10px;
}

// -------------
.patient-details {
  background-color: white;
  top: 30px;
  left: 10px;
  position: relative;
  border-radius: 8px;
  /* Ensure visibility in Electron */
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: 100%;
  z-index: 1;
}

// ------------------------- scroll-tab-css-----------------// Scrollable Tabs

.scroll-tabs-wrapper {
  width: 100%;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  white-space: nowrap;
  padding: 12px 0;
  background: white;
  border-radius: 8px;
  padding: 16px;
  border-bottom: 1px solid #E5E7EB;
  /* Electron-specific fixes */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  /* Ensure proper display */
  display: block !important;
  position: relative;

  /* Show scrollbar in Electron */
  &::-webkit-scrollbar {
    height: 8px !important;
    display: block !important;
    width: auto !important;
    background: transparent !important;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px;
    display: block !important;
  }

  &::-webkit-scrollbar-thumb {
    background: #ffffff !important;
    border-radius: 4px;
    display: block !important;
  }

  &::-webkit-scrollbar-thumb:hover {
    // background: #ffffff !important;
  }

  // .tab-container {
  //   min-width: max-content !important;
  //   display: inline-flex !important;
  //   flex-wrap: nowrap !important;
  //   width: auto !important;
  //   /* Electron-specific fixes */
  //   overflow-x: auto !important;
  //   overflow-y: hidden !important;
  //   white-space: nowrap !important;
  //   /* Ensure visibility */
  //   opacity: 1 !important;
  //   visibility: visible !important;
  // }

 }
.scroll-tabs-wrapper {
 width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  padding: 12px 0;
  background: white;
  border-radius: 8px;
  padding: 16px;
  border-bottom: 1px solid #E5E7EB;
 }
  .tab-header {
    min-width: max-content;
    display: inline-flex;
    flex-wrap: nowrap;
  }

  .button-native {
     flex-shrink: 0;
    white-space: nowrap;
    font-size: 16px;
    width: 158px;
    font-weight: 400;
    text-transform: capitalize;
    height: 56px;
    font-family: 'DM Sans', sans-serif;

    color: #4A4A48;
     /* Active state */
  &.active {
    color: #007bff !important;
    border-bottom: 2px solid #007bff !important;
  }

  &:hover {
    background: rgba(241, 241, 241, 0.05);
  }

  }








// ------------------ Tab Panel Content
.tab-content {
  width: 100%;
  padding-top: 0;
  background-color: white;
  /* Ensure content is visible */
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  min-height: 400px;

  /* Fix for Electron */
  overflow-y: auto !important;
  overflow-x: hidden;
  max-height: calc(100vh - 300px);

  /* Debug styling */
  .debug-info {
    padding: 5px;
    background: #f0f0f0;
    border-radius: 4px;
    font-family: monospace;
  }
}

.tab-panel {
  border-radius: 8px;
  /* Ensure panels are visible */
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: 100%;
  // padding: 16px;

  /* Animation for smooth transitions */
  transition: opacity 0.3s ease-in-out;
}
.cards{
  padding: 16px;
}

/* Ensure ng-container content is visible */
ng-container {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Electron-specific fixes for tab switching */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .tab-content {
    /* Force repaint in Electron */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  .tab-panel {
    /* Ensure proper rendering */
    will-change: opacity, transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}




//-------------------- past-records.component.scss
.past-records-card {
  .filter-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    // margin-top: 10px;
    padding: 16px;
  }
    .chip-container {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .icons-container {
      display: flex;
      gap: 16px;
      align-items: center;
      width: 271px;
      height: 48px;

      button {
        width: 125px;
        font-size: 14px;
        color: #007bff;
        display:flex;


        ion-icon {
          margin-right: 6px;
        }
      }
    }
  }

  .record-noti {
    display: flex;
    background: transparent;
    border: 1px solid #D1D5DB;
    border-radius: 50px;
    font-size: 12px;
    font-weight: 500;
    font-family: "DM Sans", sans-serif;
    color: #4A4A48;
    padding: 8px;
  }

  .record-noti span img {
    margin-left: 20px;
  }

  .filters-text {
    color: #007AFF;
    font-size: 14px;
    font-weight: 600;
    font-family: "DM Sans", sans-serif;
    margin-left: 9px;
    letter-spacing: 0.4px;
    line-height: 140%;
  }

  // -- table css
  /* New Table Styles */
  .new-complaints-table {
    width: 100%;
    border-collapse: collapse;
    // background: #f9f9ff;
    overflow: hidden;
    // margin-top: 40px;
  }

  .new-complaints-table th,
  .new-complaints-table td {
    // padding: 10px 14px;
    text-align: left;
    font-size: 14px;
  }

  .new-complaints-table th {
    background: #D6E9FF;
    font-weight: 400;
    color: #374151;
    /* border-bottom: 1px solid #b3c6e7; */
    font-size: 12px;
    height: 33px;
  }

  .new-complaints-table tr {
    // border-bottom: 1px solid #e3e3f3;
    height: 40px;
    font-size: 14px;
    font-weight: 500;
    left: 150%;
    color: #374151;
  }

  .new-complaints-table tr:last-child {
    border-bottom: none;
  }

  .new-complaints-table td {
    vertical-align: middle;
  }

  .action-icons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-right: 103px;
  }

  .new-action-icons .icon {
    cursor: pointer;
    font-size: 16px;
    color: #FFFFFF;
    transition: color 0.2s;
  }

  .new-action-icons .icon:hover {
    color: #1976d2;
  }

  @media (max-width: 600px) {

    .new-complaints-table th,
    .new-complaints-table td {
      padding: 7px 4px;
      font-size: 12px;
    }
  }

  .new-complaints-table th:nth-child(1),
  .new-complaints-table td:nth-child(1) {
    width: 120px;
    /* SNOMED CT */
    padding-left: 4px;
  }

  .new-complaints-table th:nth-child(2),
  .new-complaints-table td:nth-child(2) {
    width: 80px;
    /* Complaint Texting */
  }

  .new-complaints-table tr:nth-child(even) {
    background-color: #F9FAFB;
  }


  .new-complaints-table th:nth-child(3),
  .new-complaints-table td:nth-child(3) {
    width: 785px;
    /* Since */
  }

  .new-complaints-table th:nth-child(4),
  .new-complaints-table td:nth-child(4) {
    width: 150px;
    /* Action */
  }

  .new-complaints-table th:nth-child(5),
  .new-complaints-table td:nth-child(5) {
    width: 150px;
    /* Action */
  }



// ---------------- parameters.page.scss
.parameters-container {
  max-width: 1200px;
  padding: 16px;
}

.legend {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  margin-top:18px;
}

.legend-item {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  color: white;
}

.status-button {
  display: flex;
  align-items: center;
  border: 1px solid #d3d6db;
  border-radius: 9px;
  padding: 0;
  overflow: hidden;
  background-color: #f9fafb;
  font-family: 'DM Sans', sans-serif;
  font-size: 14px;
  color: #333;
  height: 32px;
  width: 123px;
}

.status-indicator {
  width: 32px;
  height: 100%;
  background-color: #d3d6db;
}

.status-label {
  padding: 0 12px;
    width: 106px;
    font-size: 11px;
    letter-spacing: 0.2px;
}


.ble-status {
  margin-left: auto;
  color: #10B981;
  font-weight: 500;
  border: 1px solid #10B981;
  padding: 8px 16px;
  width: 165px;
  font-size: 12px;
  border-radius: 72px;
  height: 36px;
}

.section-title {
  font-size: 18px;
  margin-bottom: 28px;
  margin-top: 28px;
  /* font-size: 12px; */
  font-weight: 400;
  line-height: 140%;
  color: #4B5563;
}
.section-title2 {

  padding-top: 35px;

}


.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;
}

.card {
 border: 1px solid #cac8c8;
  border-left-width: 8px;
  padding: 12px;
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 137px;
  justify-content: space-between;
  // width: 196px;
  box-shadow: 2px 2px 4px #c5c5c5;
}

.card-icon {
  /* font-size: 24px; */
  opacity: 0.7;
  display: flex;
  justify-content: space-between;
}

.img1 {
  width: 32px;
  height: 32px;
}

.img2 {
  width: 24px;
  height: 24px;
}

.card-title {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  // letter-spacing: 0.4px;

}

.card-value {
  font-size: 12px;
  color: #374151;
  font-weight: 400;
}

/* Card status color indicators (left border) */
.card.connected {
  border-left-color: #007AFF;
}

.card.disconnected {
  border-left-color: #F59E0B;
}

.card.completed {
  border-left-color: #10B981;
}

.card.disabled {
  border-left-color: #D1D5DB;
  opacity: 0.5;
}
