<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/table-navigator"></ion-back-button>
    </ion-buttons>
    <ion-title>Diagnosis Categories</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="exportData()" [disabled]="isLoading">
        <ion-icon name="download-outline"></ion-icon>
      </ion-button>
      <ion-button (click)="refreshData()" [disabled]="isLoading">
        <ion-icon name="refresh-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Diagnosis Categories</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading diagnosis categories...</p>
  </div>

  <div *ngIf="!isLoading">
    <!-- Header Card with Statistics -->
    <ion-card class="stats-card">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="folder-outline" class="title-icon"></ion-icon>
          Diagnosis Categories Table
        </ion-card-title>
        <ion-card-subtitle>Complete list of medical diagnosis categories</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col size="4">
              <div class="stat-item">
                <h2>{{ totalCount | number }}</h2>
                <p>Total Categories</p>
              </div>
            </ion-col>
            <ion-col size="4">
              <div class="stat-item">
                <h2>{{ filteredCategories.length | number }}</h2>
                <p>Filtered Results</p>
              </div>
            </ion-col>
            <ion-col size="4">
              <div class="stat-item">
                <h2>{{ ((filteredCategories.length / totalCount) * 100) | number:'1.0-0' }}%</h2>
                <p>Match Rate</p>
              </div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-card-content>
    </ion-card>

    <!-- Search Section -->
    <ion-card class="search-card">
      <ion-card-content>
        <ion-searchbar
          [(ngModel)]="searchText"
          (ionInput)="onSearch()"
          placeholder="Search categories by ID, name, or domain..."
          debounce="300"
          show-clear-button="focus">
        </ion-searchbar>

        <div class="search-info" *ngIf="searchText">
          <ion-chip color="primary">
            <ion-icon name="search-outline"></ion-icon>
            <ion-label>{{ filteredCategories.length }} results for "{{ searchText }}"</ion-label>
            <ion-icon name="close-circle" (click)="clearSearch()"></ion-icon>
          </ion-chip>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Data Table -->
    <ion-card class="table-card">
      <ion-card-header>
        <ion-card-title>Data Table</ion-card-title>
        <ion-card-subtitle>
          Showing {{ filteredCategories.length }} of {{ totalCount }} records
        </ion-card-subtitle>
      </ion-card-header>

      <ion-card-content class="table-content">
        <!-- Table Header -->
        <div class="table-wrapper">
          <table class="data-table">
            <thead>
              <tr>
                <th class="row-number">#</th>
                <th *ngFor="let column of displayColumns" [style.width]="column.width">
                  {{ column.label }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let category of filteredCategories; let i = index; trackBy: trackByCategory">
                <td class="row-number">{{ i + 1 }}</td>
                <td class="category-id">
                  <ion-badge color="primary">{{ getDisplayValue(category.CategoryId) }}</ion-badge>
                </td>
                <td class="category-name">
                  <strong>{{ getDisplayValue(category.Category) }}</strong>
                </td>
                <td class="status">
                  <ion-badge [color]="getStatusBadgeColor(category.IsDeleted)">
                    {{ getStatusText(category.IsDeleted) }}
                  </ion-badge>
                </td>
                <td class="domain">
                  <ion-chip color="secondary" outline="true">
                    <ion-label>{{ getDisplayValue(category.domain) }}</ion-label>
                  </ion-chip>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- No Results Message -->
        <div *ngIf="filteredCategories.length === 0 && !isLoading" class="no-results">
          <ion-icon name="search-outline" class="no-results-icon"></ion-icon>
          <h3>No categories found</h3>
          <p *ngIf="searchText">No categories match your search criteria "{{ searchText }}"</p>
          <p *ngIf="!searchText">No diagnosis categories available in the database</p>
          <ion-button *ngIf="searchText" (click)="clearSearch()" fill="outline">
            <ion-icon name="refresh-outline" slot="start"></ion-icon>
            Clear Search
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <ion-button expand="block" (click)="exportData()" color="primary">
        <ion-icon name="download-outline" slot="start"></ion-icon>
        Export All Data (JSON)
      </ion-button>

      <ion-button expand="block" (click)="refreshData()" fill="outline" color="secondary">
        <ion-icon name="refresh-outline" slot="start"></ion-icon>
        Refresh Data
      </ion-button>
    </div>

    <!-- Footer Info -->
    <ion-card class="footer-info">
      <ion-card-content>
        <div class="footer-content">
          <ion-icon name="information-circle-outline" class="info-icon"></ion-icon>
          <div class="info-text">
            <h4>About Diagnosis Categories</h4>
            <p>
              This table contains all medical diagnosis categories used in the RemediNova system.
              Each category groups related diagnoses for better organization and reporting.
            </p>
            <p><small>Table: tbldiagnosiscategory | Last updated: {{ getCurrentDate() }}</small></p>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
