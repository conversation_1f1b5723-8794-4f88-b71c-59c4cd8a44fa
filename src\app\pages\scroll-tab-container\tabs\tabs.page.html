 <div class="scroll-tabs-wrapper">
          <div class="tab-container"></div>
          <div class="tab-header">
            <button (click)="switchTab('patientHistory')" class="button-native">Patient History</button>
            <button (click)="switchTab('complaints')" class="button-native">Complaints</button>
            <button (click)="switchTab('pastRecords')" class="button-native">Past Records</button>
            <button (click)="switchTab('parameters')" class="button-native">Parameters</button>
            <button (click)="switchTab('diagnosis')" class="button-native">Diagnosis</button>
            <button (click)="switchTab('medicines')" class="button-native">Medicines</button>
            <button (click)="switchTab('intervention')" class="button-native">Intervention</button>
            <button (click)="switchTab('investigations')" class="button-native">Investigations</button>
            <button (click)="switchTab('counseling')" class="button-native">Counseling</button>
            <button (click)="switchTab('referrals')" class="button-native">Referrals</button>
          </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" [attr.data-selected-tab]="selectedTab">
          <ng-container [ngSwitch]="selectedTab">

            <div *ngSwitchCase="'patientHistory'" class="tab-panel">

                <app-patient-history></app-patient-history>

            </div>

            <!-- --------------------------------- Complaints -----------------------------  -->

            <div *ngSwitchCase="'complaints'" class="tab-panel">
              <app-complaints></app-complaints>
            </div>

            <!-- -------------- pastRecords ----------------------  -->

            <div *ngSwitchCase="'pastRecords'" class="tab-panel">
              <app-pastrecords></app-pastrecords>
            </div>

            <!-- -------------------------- parameters -------------------  -->
            <div *ngSwitchCase="'parameters'" class="tab-panel">

              <div class="parameters-container cards">
                <div class=" ion-inherit-color">parameters</div>
                <!-- Legend -->
                <div class="legend">
                  <button class="status-button disabled-button">
                    <span class="status-indicator" style="background-color: #007AFF;"></span>
                    <span class="status-label">Connected</span>
                  </button>

                  <button class="status-button disabled-button">
                    <span class="status-indicator" style="background-color: #F59E0B;"></span>
                    <span class="status-label">disconnected</span>
                  </button>

                  <button class="status-button disabled-button">
                    <span class="status-indicator" style="background-color: #10B981;"></span>
                    <span class="status-label">Completed</span>
                  </button>

                  <button class="status-button disabled-button">
                    <span class="status-indicator"></span>
                    <span class="status-label">Disabled</span>
                  </button>

                  <span class="ble-status">BLE Dongle Connected</span>
                </div>

                <!-- Section Title -->
                <h3 class="section-title section-title2">Physiology Tests</h3>

                <!-- Cards Grid -->
                <div class="card-grid">
                  <!-- Sample Card -->
                  <div class="card completed">
                    <div class="card-icon"><img src="assets/icon/temp.png" alt=""> <img src="assets/icon/sign.png"
                        alt="" class="img2"></div>
                    <div class="card-title">Temperature</div>
                    <div class="card-value">98.6°F</div>
                  </div>

                  <div class="card disconnected">
                    <div class="card-icon"><img src="assets/icon/temp.png" alt="" class="img1"> <img
                        src="assets/icon/unplug.png" alt="" class="img2"></div>
                    <div class="card-title">Temperature (ASHA+)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card completed">
                    <div class="card-icon"><img src="assets/icon/stethoscope.png" alt="" class="img1"> <img
                        src="assets/icon/sign.png" alt="" class="img2"></div>
                    <div class="card-title">Stethoscope</div>
                    <div class="card-value">Normal</div>
                  </div>

                  <div class="card disconnected">
                    <div class="card-icon"><img src="assets/icon/stethoscope.png" alt="" class="img1"> <img
                        src="assets/icon/unplug.png" alt="" class="img2"></div>
                    <div class="card-title">Stethoscope (ASHA+)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/spirometer.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Spirometer</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card completed">
                    <div class="card-icon"><img src="assets/icon/bloodpressure.png" alt=""> <img
                        src="assets/icon/sign.png" alt="" class="img2"></div>
                    <div class="card-title">Blood Pressure</div>
                    <div class="card-value">120/80 mmHg</div>
                  </div>

                  <div class="card completed">
                    <div class="card-icon"><img src="assets/icon/spo.png" alt=""> <img src="assets/icon/sign.png" alt=""
                        class="img2"></div>
                    <div class="card-title">Spo2</div>
                    <div class="card-value">98%</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/ecg.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">ECG</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card disabled">
                    <div class="card-icon"><img src="assets/icon/ECGInterpretation.png" alt="" class="img1">
                      <img src="assets/icon/disable.png" alt="" class="img2">
                    </div>
                    <div class="card-title">ECG Interpretation</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/fetaldoppler.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Fetal Doppler (FD)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/fetaldoppler.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Fetal Doppler (Fetosense)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/refractometer.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Auto Refractometer</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/xrays.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">X-Ray</div>
                    <div class="card-value">-</div>
                  </div>

                </div>
                <h3 class="section-title">Blood And Urine Tests</h3>
                <!-- Cards Grid -->
                <div class="card-grid">
                  <!-- Sample Card -->
                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/glucose.png" alt=""> <img src="assets/icon/plug.png"
                        alt="" class="img2"></div>
                    <div class="card-title">Glucose</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/glucose.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Glucose (Wireless)</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/hemoglobin.png" alt="" class="img1"><img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Hemoglobin</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/lipidprofile.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Lipid Profile</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/OpticalReader.png" alt="" class="img1"> <img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">Optical Reader</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/urin.png" alt=""><img src="assets/icon/plug.png" alt=""
                        class="img2"></div>
                    <div class="card-title">Urine Test</div>
                    <div class="card-value">-</div>
                  </div>

                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/bloodpressure.png" alt=""><img
                        src="assets/icon/plug.png" alt="" class="img2"></div>
                    <div class="card-title">HbA1c (Wireless)</div>
                    <div class="card-value">-</div>
                  </div>
                  <div class="card connected">
                    <div class="card-icon"><img src="assets/icon/hemoglobin.png" alt=""><img src="assets/icon/plug.png"
                        alt="" class="img2"></div>
                    <div class="card-title">WBC Differential</div>
                    <div class="card-value">-</div>
                  </div>





                </div>
              </div>


            </div>


            <!-- ---------------------- diagnosis  -->
            <ng-container *ngSwitchCase="'diagnosis'">
             <app-diagnosis></app-diagnosis>
            </ng-container>

            <!-- ----------------------------  --> <!-- medicines -->
            <ng-container *ngSwitchCase="'medicines'">
             <app-medicines></app-medicines>
            </ng-container>
            <!-- -------------------------  -->


            <!-- --------------- intervention -->
            <ng-container *ngSwitchCase="'intervention'">
             <app-intervention></app-intervention>
            </ng-container>

            <!-- ---------- Investigations -----------  -->

            <div *ngSwitchCase="'investigations'" class="tab-panel">
              <app-investigations></app-investigations>
            </div>

            <!-- --------------------------- consunseling -----------------------  -->
            <div *ngSwitchCase="'counseling'" class="tab-panel">
             <app-counseling></app-counseling>
            </div>

            <!-- ----------------------- referrals -----------------  -->
            <div *ngSwitchCase="'referrals'" class="tab-panel">
            <app-referrals></app-referrals>
            </div>

            <!-- ------------------  -->
          </ng-container>
