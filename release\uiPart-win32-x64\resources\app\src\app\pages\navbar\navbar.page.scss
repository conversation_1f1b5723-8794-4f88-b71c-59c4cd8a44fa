 .container {
   display: flex;
   flex-direction: column;
   min-height: 100vh;
   width: 100%;
 }

 /* Header */
 .header {
   background-color: #ffffff;
   border-bottom: 1px solid #e5e7eb;
   // padding: 16px;
   height: 80px;

   display: flex;
   justify-content: space-between;
   align-items: center;
   margin: 0 auto;
 }

 .header-content {
  //  display: flex;
   margin: 0 auto;
 }
 .logo-container{
  display: flex;
  width:282px ;
  height: 32px;
  justify-content: space-between;
 }

 .logo {
   width: 88px;
   height: 32px;
 }



 .brand-text {
     width: 150px;
    height: 17px;
    font-size: 24px;
    font-weight: 400;
    line-height: 28px;
    letter-spacing: 0px;
    position: relative;
    // top: 7.5px;
    // left: 132px;
 }

 .brand-text .partner {
   color: #082bab;
 }

 .brand-text .logo-text {
   color: #089bab;
 }

 .brand-text .plus {
   color: #ff7d7d;
 }

 .search-container {
   position: relative;
   width: 320px;
   height: 48px;
 }

 .search-input {
   width: 100%;
   padding: 14px 38px 14px 14px;
   border: 1px solid #d1d5db;
   border-radius: 8px;
   background-color: #ffffff;
   font-size: 14px;
   font-family: 'DM Sans', sans-serif;
   color: #374151;
 }

 .search-icon {
   position: absolute;
   right: 14px;
   top: 50%;
   transform: translateY(-50%);
   width: 24px;
   height: 24px;
 }

 .header-actions {
   display: flex;
   width: 109px;
    gap: 16px;
    height: 24px;
 }

 .header-icon {
   width: 24px;
   height: 24px;
   cursor: pointer;
 }

 .profile-image {
   width: 24px;
   height: 24px;
   border-radius: 12px;
 }
