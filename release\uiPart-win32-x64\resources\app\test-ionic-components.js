const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    },
    show: true
  });

  mainWindow.webContents.openDevTools();
  
  mainWindow.loadFile(path.join(__dirname, 'www/index.html'));

  mainWindow.webContents.on('dom-ready', () => {
    console.log('DOM ready, checking Ionic components...');
    
    // Wait for Angular to load, then check Ionic components
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        console.log('=== CHECKING IONIC COMPONENTS ===');
        
        // Check for ion-card
        const ionCards = document.querySelectorAll('ion-card');
        console.log('Found ion-card elements:', ionCards.length);
        
        if (ionCards.length > 0) {
          ionCards.forEach((card, index) => {
            console.log('Ion-card', index, 'styles:', {
              display: getComputedStyle(card).display,
              opacity: getComputedStyle(card).opacity,
              visibility: getComputedStyle(card).visibility,
              background: getComputedStyle(card).backgroundColor,
              borderRadius: getComputedStyle(card).borderRadius,
              boxShadow: getComputedStyle(card).boxShadow
            });
          });
        }
        
        // Check for other Ionic components
        const ionButtons = document.querySelectorAll('ion-button');
        console.log('Found ion-button elements:', ionButtons.length);
        
        const ionInputs = document.querySelectorAll('ion-input');
        console.log('Found ion-input elements:', ionInputs.length);
        
        const ionCheckboxes = document.querySelectorAll('ion-checkbox');
        console.log('Found ion-checkbox elements:', ionCheckboxes.length);
        
        const ionSegments = document.querySelectorAll('ion-segment');
        console.log('Found ion-segment elements:', ionSegments.length);
        
        // Check if Ionic components have proper classes
        const ionicElements = document.querySelectorAll('[class*="ion-"]');
        console.log('Found elements with ion- classes:', ionicElements.length);
        
        console.log('=== END IONIC COMPONENTS CHECK ===');
      `);
    }, 3000);
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
