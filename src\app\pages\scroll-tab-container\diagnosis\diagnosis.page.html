<div class="diagnosis-card cards">
  <ion-card-header>
    <div class="ion-inherit-color">Diagnosis</div>
  </ion-card-header>

  <div>
    <label class="diagnosis-header">How would you like to add Diagnosis?</label>

    <div class="select-diagnosis">
      <span class="diagnosis-radio-item">
        <input
          type="radio"
          name="diagnosisMode"
          value="select"
          [(ngModel)]="diagnosisMode"
        />
        <label>Select Diagnosis</label>
      </span>

      <span class="diagnosis-radio-item" style="border: none;">
        <input
          type="radio"
          name="diagnosisMode"
          value="chapter"
          [(ngModel)]="diagnosisMode"
        />
        <label>Select Chapter</label>
      </span>

      <span class="diagnosis-radio-item">
        <input
          type="radio"
          name="diagnosisMode"
          value="manual"
          [(ngModel)]="diagnosisMode"
        />
        <label>Enter Diagnosis Manually (Free Text)</label>
      </span>
    </div>

    <div class="diagnosis-input-group">
      <div class="diagnosis-checkbox">
        <label>Diagnosis</label>
        <div>
          <input
            type="checkbox"
            [(ngModel)]="isProvisional"
            style="margin-right: 10px;"
          />
          <label>Provisional</label>
        </div>
      </div>

      <div class="dig-input-contain">
        <!-- ✅ Show Dropdown or Input based on Mode -->
        <ng-container [ngSwitch]="diagnosisMode">
          <!-- Diagnosis Dropdown -->
          <select
            *ngSwitchCase="'select'"
            class="diagnosis-input"
            [(ngModel)]="selectedDiagnosis"
          >
            <option value="">-- Select Diagnosis --</option>
            <option
              *ngFor="let diag of diagnosisList"
              [value]="diag.Category"
            >
              {{ diag.Category }}
            </option>
          </select>

          <!-- Chapter Dropdown -->
          <select
            *ngSwitchCase="'chapter'"
            class="diagnosis-input"
            [(ngModel)]="selectedDiagnosis"
          >
            <option value="">-- Select Chapter --</option>
            <option
              *ngFor="let chap of chapterList"
              [value]="chap.chapter"
            >
              {{ chap.chapter }}
            </option>
          </select>

          <!-- Manual Input -->
          <input
            *ngSwitchCase="'manual'"
            class="diagnosis-input"
            type="text"
            [(ngModel)]="selectedDiagnosis"
            placeholder="Enter Diagnosis Manually"
          />
        </ng-container>

        <span (click)="addDiagnosis()"
          ><img src="assets/icon/plus.png" alt="" /> Add</span
        >
      </div>
    </div>
  </div>
</div>

<!-- Table -->
<table class="new-complaints-table">
  <thead>
    <tr>
      <th>ICD Code</th>
      <th>Diagnosis</th>
      <th>Provisional</th>
      <th>Action</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let item of diagnoses">
      <td>{{ item.code }}</td>
      <td>{{ item.name }}</td>
      <td>{{ item.provisional ? 'Yes' : 'No' }}</td>
      <td>
        <span class="action-icons">
          <span class="icon" title="Edit" (click)="editDiagnosis(item)"
            ><img src="assets/icon/edit.png" alt=""
          /></span>
          <span class="icon" title="Delete" (click)="deleteDiagnosis(item)"
            ><img src="assets/icon/delete.png" alt=""
          /></span>
        </span>
      </td>
    </tr>
  </tbody>
</table>







 <!-- <div class="diagnosis-card cards">
                <ion-card-header>
                  <div class=" ion-inherit-color">Diagnosis</div>
                </ion-card-header>
                <div>

                  <label class="diagnosis-header">How would you like to add Diagnosis?</label>

                  <div class="select-diagnosis">
                    <span class="diagnosis-radio-item">

                      <input type="radio" slot="start" value="select">
                      <label>Select Diagnosis</label>
                    </span>

                    <span class="diagnosis-radio-item" style="border: none;">

                      <input type="radio" slot="start" value="chapter">
                      <label>Select Chapter</label>
                    </span>

                    <span class="diagnosis-radio-item">

                      <input type="radio" slot="start" value="manual">
                      <label>Enter Diagnosis Manually (Free Text)</label>
                    </span>
                  </div>
                  <div class="diagnosis-input-group">
                    <div class="diagnosis-checkbox">
                      <label>Diagnosis</label>
                      <div>
                        <input type="checkbox" [(ngModel)]="isProvisional" style="margin-right: 10px;">
                        <label>Provisional</label>
                      </div>
                    </div>

                    <div class="dig-input-contain">

                      <input class="diagnosis-input" type="text" [(ngModel)]="selectedDiagnosis"
                        placeholder="Select or Enter Diagnosis">

                      <span (click)="addDiagnosis()"><img src="assets/icon/plus.png" alt=""> Add</span>
                    </div>
                  </div>
                </div>
              </div>



              <table class="new-complaints-table">
                <thead>
                  <tr>
                    <th>ICD Code</th>
                    <th>Diagnosis</th>
                    <th>Provisional</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>A42.1</td>
                    <td>Abdominal Actinomycosis</td>
                    <td>Yes</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>A19.0</td>
                    <td>Acute Miliary Tuberculosis Of A Single Specified Site</td>
                    <td>Yes</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>J00</td>
                    <td>Acute Nasopharyngitis [Common Cold]</td>
                    <td>No</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Y45.5</td>
                    <td>4-Aminophenol Derivatives</td>
                    <td>No</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table> -->
