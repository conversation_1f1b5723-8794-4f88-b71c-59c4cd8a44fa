<!-- ------------  -->
              <div class="past-records-card cards" style="margin: 0;">
                <ion-card-header>
                  <div class=" ion-inherit-color">Past Records</div>
                  <div class="filter-actions">
                    <div class="chip-container">
                      <div class="record-noti">
                        <label>1st May July, 2025 - 6th May, 2025 </label>
                        <span (click)="removeFilter('date')"><img src="assets/icon/cross.png" alt=""></span>
                      </div>

                      <div class="record-noti">
                        <label>Newest First </label>
                        <span (click)="removeFilter('sort')"><img src="assets/icon/cross.png" alt=""></span>
                      </div>
                    </div>

                    <div class="icons-container">
                      <button fill="clear" size="small">
                        <img src="assets/icon/filter.png" alt=""><span class="filters-text"> Filters</span>
                      </button>
                      <button fill="clear" size="small">
                        <img src="assets/icon/upload.png" alt=""><span class="filters-text">Upload</span>
                      </button>
                    </div>
                  </div>
                </ion-card-header>
              </div>
              <!-- --------------- table  -->
              <table class="new-past-table" style="margin-top: 6px;">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Past Records</th>
                    <th>Attended By</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>05th May,2025</td>
                    <td>09:15 AM</td>
                    <td>General Xray (1 Image)</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>05th May,2025</td>
                    <td>09:30 AM</td>
                    <td>Dermatoscope (4 Images)</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>05th May,2025</td>
                    <td>09:45 AM</td>
                    <td>ECG (1 Image)</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>05th May,2025</td>
                    <td>10:00 AM</td>
                    <td>Report No. 124</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>05th May,2025</td>
                    <td>10:30 AM</td>
                    <td>Report No. 122</td>
                    <td>Nr. Shilpa Sharma</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>

                  </tr>
                </tbody>
              </table>
              <!-- ---------------- button -->
              <div>

                <div class="pagination-container">
                  <select interface="popover" placeholder="05 per page" [(ngModel)]="pageSize">
                    <option *ngFor="let size of pageSizes" [value]="size">{{ size }} per
                      page</option>
                  </select>

                  <div class="page-btn">

                    <div class="page-info">
                      {{ currentPageRange }} of {{ totalItems }}
                    </div>

                    <span style="border-left: 1px solid rgb(236, 236, 236);margin-left: 7px;
    height: 43px;" [ngClass]="{ 'disabled': currentPage === 1 }" (click)="currentPage !== 1 && previousPage()">
                      <img src="assets/icon/left-arraw.png" alt="">
                    </span>

                    <span style="height: 43px;" [ngClass]="{ 'disabled': currentPage === totalPages } "
                      (click)="currentPage !== totalPages && nextPage()">
                      <img src="assets/icon/light-arraw.png" alt="">
                    </span>

                  </div>
                </div>
              </div>

              <!-- -------------  -->
