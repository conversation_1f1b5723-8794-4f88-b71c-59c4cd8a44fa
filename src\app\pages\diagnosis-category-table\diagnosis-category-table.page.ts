import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RemedinovaDataService } from '../../services/remedinova-data.service';
import { DataLoaderService } from '../../services/data-loader.service';
import { PouchService } from '../../services/pouch.service';

interface DiagnosisCategory {
  CategoryId: string;
  Category: string;
  IsDeleted: string;
  domain: string;
}

@Component({
  selector: 'app-diagnosis-category-table',
  templateUrl: './diagnosis-category-table.page.html',
  styleUrls: ['./diagnosis-category-table.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class DiagnosisCategoryTablePage implements OnInit {

  diagnosisCategories: DiagnosisCategory[] = [];
  filteredCategories: DiagnosisCategory[] = [];
  isLoading = false;
  searchText = '';
  totalCount = 0;

  // Table columns to display
  displayColumns = [
    { key: 'CategoryId', label: 'Category ID', width: '20%' },
    { key: 'Category', label: 'Category Name', width: '50%' },
    { key: 'IsDeleted', label: 'Status', width: '15%' },
    { key: 'domain', label: 'Domain', width: '15%' }
  ];

  constructor(
    private remedinovaData: RemedinovaDataService,
    private dataLoader: DataLoaderService,
    private pouchService: PouchService
  ) {}

  async ngOnInit() {
    await this.loadDiagnosisCategories();
  }

  async loadDiagnosisCategories() {
    this.isLoading = true;

    try {
      // Ensure data is loaded first
      await this.dataLoader.ensureDataLoaded();

      // Get all diagnosis categories
      const categories = await this.pouchService.getAllFromTable('tbldiagnosiscategory');

      this.diagnosisCategories = categories.map(category => ({
        CategoryId: category.CategoryId || '',
        Category: category.Category || '',
        IsDeleted: category.IsDeleted || '',
        domain: category.domain || ''
      }));

      this.filteredCategories = [...this.diagnosisCategories];
      this.totalCount = this.diagnosisCategories.length;

      console.log(`✅ Loaded ${this.totalCount} diagnosis categories`);

    } catch (error) {
      console.error('❌ Error loading diagnosis categories:', error);
    } finally {
      this.isLoading = false;
    }
  }

  onSearch() {
    if (!this.searchText.trim()) {
      this.filteredCategories = [...this.diagnosisCategories];
      return;
    }

    const searchTerm = this.searchText.toLowerCase();
    this.filteredCategories = this.diagnosisCategories.filter(category => {
      return (
        category.CategoryId.toLowerCase().includes(searchTerm) ||
        category.Category.toLowerCase().includes(searchTerm) ||
        category.domain.toLowerCase().includes(searchTerm)
      );
    });
  }

  clearSearch() {
    this.searchText = '';
    this.filteredCategories = [...this.diagnosisCategories];
  }

  async refreshData() {
    await this.loadDiagnosisCategories();
  }

  async exportData() {
    try {
      const dataToExport = {
        tableName: 'tbldiagnosiscategory',
        exportDate: new Date().toISOString(),
        totalRecords: this.diagnosisCategories.length,
        data: this.diagnosisCategories
      };

      const dataStr = JSON.stringify(dataToExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `diagnosis_categories_${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      URL.revokeObjectURL(url);

      console.log('✅ Data exported successfully');

    } catch (error) {
      console.error('❌ Error exporting data:', error);
    }
  }

  getStatusBadgeColor(isDeleted: string): string {
    switch (isDeleted?.toLowerCase()) {
      case '0':
      case 'false':
      case 'no':
        return 'success';
      case '1':
      case 'true':
      case 'yes':
        return 'danger';
      default:
        return 'medium';
    }
  }

  getStatusText(isDeleted: string): string {
    switch (isDeleted?.toLowerCase()) {
      case '0':
      case 'false':
      case 'no':
        return 'Active';
      case '1':
      case 'true':
      case 'yes':
        return 'Deleted';
      default:
        return 'Unknown';
    }
  }

  getDisplayValue(value: any): string {
    if (value === null || value === undefined || value === '') {
      return '-';
    }
    return value.toString();
  }

  trackByCategory(index: number, category: DiagnosisCategory): string {
    return category.CategoryId;
  }

  getCurrentDate(): string {
    return new Date().toLocaleString();
  }
}
