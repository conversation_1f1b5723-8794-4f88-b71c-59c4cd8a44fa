// Loading container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;

  ion-spinner {
    margin-bottom: 1rem;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
    text-align: center;
  }
}

// Welcome card
.welcome-card {
  margin: 1rem;
  background: linear-gradient(135deg, var(--ion-color-primary-tint), var(--ion-color-secondary-tint));
  color: white;

  ion-card-header {
    ion-card-title {
      display: flex;
      align-items: center;
      font-size: 1.3rem;
      font-weight: 600;

      .title-icon {
        font-size: 1.5rem;
        margin-right: 0.5rem;
      }
    }

    ion-card-subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 0.95rem;
      line-height: 1.4;
    }
  }

  .status-indicator {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    &.ready {
      background: rgba(76, 175, 80, 0.2);
    }

    &.loading {
      background: rgba(255, 193, 7, 0.2);
    }

    ion-icon {
      font-size: 1.2rem;
      margin-right: 0.5rem;
    }

    span {
      font-weight: 500;
    }
  }
}

// Stats card
.stats-card {
  margin: 1rem;

  .stat-item {
    text-align: center;
    padding: 0.5rem;

    h2 {
      font-size: 2rem;
      font-weight: 700;
      color: var(--ion-color-primary);
      margin: 0 0 0.25rem 0;
    }

    p {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .top-tables {
    margin-top: 1.5rem;

    h4 {
      font-size: 1rem;
      color: var(--ion-color-primary);
      margin-bottom: 0.75rem;
      font-weight: 600;
    }

    ion-list {
      background: transparent;

      ion-item {
        --padding-start: 0;
        --inner-padding-end: 0;

        ion-label {
          h3 {
            font-size: 0.9rem;
            font-weight: 500;
          }

          p {
            font-size: 0.8rem;
            color: var(--ion-color-medium);
          }
        }
      }
    }
  }
}

// Navigation section
.navigation-section {
  padding: 0 1rem;

  .section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--ion-color-primary);
    margin: 1.5rem 0 1rem 0;
    text-align: center;
  }

  .navigation-cards {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
}

// Navigation cards
.nav-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.nav-card-primary {
    border-left: 4px solid var(--ion-color-primary);
  }

  &.nav-card-secondary {
    border-left: 4px solid var(--ion-color-secondary);
  }

  &.nav-card-tertiary {
    border-left: 4px solid var(--ion-color-tertiary);
  }

  &.nav-card-success {
    border-left: 4px solid var(--ion-color-success);
  }

  .nav-card-header {
    display: flex;
    align-items: center;

    .nav-icon {
      font-size: 2rem;
      margin-right: 1rem;
    }

    .nav-info {
      flex: 1;

      ion-card-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
      }

      ion-card-subtitle {
        font-size: 0.85rem;
        color: var(--ion-color-medium);
      }
    }

    .nav-arrow {
      font-size: 1.2rem;
      color: var(--ion-color-medium);
    }
  }

  ion-card-content {
    padding-top: 0;

    p {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      line-height: 1.4;
      margin: 0;
    }
  }
}

// Actions card
.actions-card {
  margin: 1rem;

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .action-btn {
      --border-radius: 8px;
      height: 48px;
    }
  }
}

// Info footer
.info-footer {
  margin: 1rem;
  background: var(--ion-color-light-tint);

  .info-content {
    display: flex;
    align-items: flex-start;

    .info-icon {
      font-size: 1.5rem;
      color: var(--ion-color-primary);
      margin-right: 1rem;
      margin-top: 0.25rem;
      flex-shrink: 0;
    }

    .info-text {
      flex: 1;

      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--ion-color-primary);
        margin: 0 0 0.5rem 0;
      }

      p {
        font-size: 0.85rem;
        color: var(--ion-color-medium);
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .welcome-card,
  .stats-card,
  .actions-card,
  .info-footer {
    margin: 0.5rem;
  }

  .navigation-section {
    padding: 0 0.5rem;
  }

  .welcome-card {
    ion-card-header {
      ion-card-title {
        font-size: 1.1rem;

        .title-icon {
          font-size: 1.3rem;
        }
      }

      ion-card-subtitle {
        font-size: 0.85rem;
      }
    }
  }

  .stats-card {
    .stat-item {
      h2 {
        font-size: 1.6rem;
      }

      p {
        font-size: 0.8rem;
      }
    }
  }

  .nav-card {
    .nav-card-header {
      .nav-icon {
        font-size: 1.6rem;
        margin-right: 0.75rem;
      }

      .nav-info {
        ion-card-title {
          font-size: 1rem;
        }

        ion-card-subtitle {
          font-size: 0.8rem;
        }
      }
    }
  }

  .info-footer {
    .info-content {
      .info-icon {
        font-size: 1.3rem;
        margin-right: 0.75rem;
      }

      .info-text {
        h4 {
          font-size: 0.9rem;
        }

        p {
          font-size: 0.8rem;
        }
      }
    }
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .welcome-card {
    background: linear-gradient(135deg, var(--ion-color-primary-shade), var(--ion-color-secondary-shade));
  }

  .nav-card {
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
  }

  .info-footer {
    background: var(--ion-color-dark-tint);
  }
}
