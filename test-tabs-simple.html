<!DOCTYPE html>
<html>
<head>
    <title>Tab Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        .scroll-tabs-wrapper {
            width: 100%;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            padding: 16px;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .scroll-tabs-wrapper::-webkit-scrollbar {
            height: 8px;
        }
        
        .scroll-tabs-wrapper::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .scroll-tabs-wrapper::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .tab-button {
            display: inline-block;
            padding: 12px 20px;
            margin-right: 8px;
            background: transparent;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            color: #333;
            font-size: 16px;
            font-weight: 400;
            white-space: nowrap;
            min-width: 158px;
            text-align: center;
            cursor: pointer;
            text-decoration: none;
        }
        
        .tab-button:hover {
            background: #f5f5f5;
        }
        
        .tab-button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <h1>Horizontal Tabs Test</h1>
    
    <div class="scroll-tabs-wrapper">
        <a href="#" class="tab-button active">Patient History</a>
        <a href="#" class="tab-button">Complaints</a>
        <a href="#" class="tab-button">Past Records</a>
        <a href="#" class="tab-button">Parameters</a>
        <a href="#" class="tab-button">Diagnosis</a>
        <a href="#" class="tab-button">Medicines</a>
        <a href="#" class="tab-button">Intervention</a>
        <a href="#" class="tab-button">Investigations</a>
        <a href="#" class="tab-button">Counseling</a>
        <a href="#" class="tab-button">Referrals</a>
    </div>
    
    <p>This is a test to verify horizontal scrolling tabs work in Electron.</p>
    
    <script>
        console.log('Tab test loaded');
        
        // Add click handlers
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all buttons
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                // Add active class to clicked button
                button.classList.add('active');
                
                console.log('Tab clicked:', button.textContent);
            });
        });
    </script>
</body>
</html>
