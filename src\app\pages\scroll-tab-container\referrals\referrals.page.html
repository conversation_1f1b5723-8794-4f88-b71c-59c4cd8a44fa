<div class="medicines-section">
                <div class="medicines-header">
                  <div class=" ion-inherit-color">Referrals</div>
                  <a class="previous-prescription" href="#">
                    <span class="icon"></span>

                  </a>
                </div>
                <div class="medicines-cont">
                  <form class="medicines-form" style="grid-template-columns: repeat(1, 1fr)">


                    <div class="form-group">
                      <label class="form-label">Specialization <span class="required">*</span></label>
                      <select class="form-select" required [(ngModel)]="selectedSpeciality"
        name="speciality"
        [disabled]="loading || !!error">

                        <option value="" disabled selected>Select Specialization</option>
                        <option *ngFor="let s of specialityList" [value]="s.speciality">
          {{ s.speciality }}
        </option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label">Referral Note <span class="required">*</span></label>
                      <input type="text" class="form-select" required placeholder="Type Referral Note *"
                        style="background: none; height: 96px;">

                    </div>

                    <!-- <div class="form-group add-btn-group">
                    <label class="form-label">Days <span class="required">*</span></label>
                      <input class="form-control" type="" placeholder="Enter Days" required />
                     </div> -->

                  </form>
                  <button type="button" class="add-btn" style="align-items: flex-end; margin-bottom :30px;">
                    <span class="icon"><img src="assets/icon/plus.png" alt=""></span>
                    Add
                  </button>
                </div>
              </div>
              <!-- ------------- table  -->

              <table class="referral-table">
                <thead>
                  <tr>
                    <th>Specialization</th>
                    <th>Referral Note</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Anesthesia</td>
                    <td>Lorem Ipsum</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Cardiac Surgery</td>
                    <td>Lorem Ipsum</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Dermatology</td>
                    <td>Lorem Ipsum</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>ENT</td>
                    <td>Lorem Ipsum</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
