

.cards {
    padding: 16px;
}
input,
select {
  border: none;
  outline: none;
  box-shadow: none;
  background: transparent; /* Optional: if you want transparent background */
}
button{
  background-color: transparent;
}
.ion-inherit-color {
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 700;
    color: #4A4A48;
    transform: rotate(0deg);
    gap: 10px;
}

// --------------------------- medicin page css -----------------
.medicines-section {
  max-width: 1200px;
  margin: 0 auto;
}

.medicines-cont {
  display: flex;
  padding: 16px;
}

.medicines-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 18px;
  padding: 16px;
}

.medicines-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  margin-bottom: 8px;
}

.previous-prescription {
  display: flex;
  align-items: center;
  color: #1976d2;
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  gap: 6px;
  transition: color 0.2s;
}

.previous-prescription:hover {
  color: #1251a3;
}

.previous-prescription .icon {
  font-size: 18px;
  display: inline-block;
  transform: rotate(-90deg);
}

.medicines-form {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  background: transparent;
  width: 1037px;
  // height: 196px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 7px;
}

.form-label {
  font-size: 13px;
  color: #6c6f7a;
  font-weight: 500;
}

.form-control,
.form-select {
  border: 1px solid #d3d7df;
  border-radius: 8px;
  padding: 12px 14px;
  font-size: 15px;
  font-family: inherit;
  color: #222;
  background: #fff;
  outline: none;
  transition: border 0.2s;
}

.form-control:focus,
.form-select:focus {
  border-color: #1976d2;
}

.form-select {
  appearance: none;
  background: url('data:image/svg+xml;utf8,<svg fill="%23a0a4ae" height="18" viewBox="0 0 24 24" width="18" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>') no-repeat right 12px center/18px 18px;
}

.required {
  color: #e53935;
  margin-left: 2px;
}

.add-btn-group {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  margin-top: 8px;
}

.add-btn {
  display: flex;
  align-items: flex-end;
  width: 120px;
  gap: 8px;
  background: none;
  border: none;
  font-weight: 600;
  cursor: pointer;
  padding: 0 8px;
  transition: color 0.2s;
  margin-left: 39px;
  font-size: 17px;
  color: #007AFF;
  font-weight: 600;
}

.add-btn .icon {
  font-size: 20px;
  font-weight: 700;
}

.add-btn:hover {
  color: #1251a3;
}

@media (max-width: 900px) {
  .medicines-form {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 600px) {
  .medicines-form {
    grid-template-columns: 1fr;
  }

  .medicines-section {
    padding: 0;
  }
}


