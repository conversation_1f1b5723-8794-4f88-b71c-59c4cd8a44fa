import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';

@Component({
  selector: 'app-medicines',
  templateUrl: './medicines.page.html',
  styleUrls: ['./medicines.page.scss', '../diagnosis/diagnosis.page.scss' ,'../tabs/tabs.page.scss'],

  standalone: true,
  imports: [ CommonModule, FormsModule]
})
export class MedicinesPage implements OnInit {

  constructor() { }

  ngOnInit() {
  }
// ------------------- medicien tab
newMedicine = {
  drugForm: '',
  name: '',
  instructions: '',
  dosage: '',
  frequency: '',
  days: null,
};

diagnosisList = [
  { code: 'A42.1', name: 'Abdominal Actinomycosis', provisional: true },
  { code: 'A19.0', name: 'Acute Military Tuberculosis Of A Single Specified Site', provisional: true },
  { code: 'J00', name: 'Acute Nasopharyngitis [Common Cold]', provisional: false },
  { code: 'Y45.5', name: '4-Aminophenol Derivatives', provisional: false }
];

addMedicine() {
  console.log('Adding medicine:', this.newMedicine);
  // push to list / save to backend as needed
}

fetchPreviousPrescription() {
  console.log('Fetching previous prescriptions...');
}

}
