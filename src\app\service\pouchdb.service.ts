import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import PouchDB from 'pouchdb-browser';

export interface MasterData {
  [key: string]: any;
}

@Injectable({ providedIn: 'root' })
export class PouchdbService {
  private db!: PouchDB.Database;

  constructor() {
    this.initDB('remedi_db');
  }

  private initDB(name: string) {
    this.db = new PouchDB(name, { adapter: 'idb' });
    console.debug(`PouchDB initialized: ${name}`);
  }

  /** Store or update the entire master data JSON under fixed ID */
  addOrUpdateMasterData(data: MasterData): Observable<PouchDB.Core.Response> {
    const docId = 'master_data';
    return from(
      this.db
        .get<MasterData>(docId)
        .then((existing) => {
          // Merge but keep existing metadata
          const merged = { ...existing, ...data, _id: docId, _rev: existing._rev };
          return this.db.put(merged as any);
        })
        .catch((err) => {
          if (err.status === 404) {
            // Create new
            return this.db.put({ _id: docId, ...data } as any);
          }
          return Promise.reject(err);
        })
    ).pipe(catchError((e) => throwError(() => this.normalizeError(e))));
  }

  /** Fetch whole master data object (without _id/_rev in returned payload) */
  getMasterData(): Observable<MasterData> {
    return from(this.db.get<MasterData>('master_data')).pipe(
      map((res: any) => {
        const { _id, _rev, ...clean } = res;
        return clean as MasterData;
      }),
      catchError((e) => throwError(() => this.normalizeError(e)))
    );
  }

  /** Fetch specific table (e.g., 'tbldiagnosiscategory') from master_data */
  getMasterTable(tableName: string): Observable<any[]> {
    return from(this.db.get<MasterData>('master_data')).pipe(
      map((res: any) => {
        // Direct table
        if (res[tableName]) {
          return res[tableName];
        }

        // If wrapped in a `.data` array structure, search inside
        if (Array.isArray(res.data)) {
          for (const item of res.data) {
            if (item[tableName]) {
              return item[tableName];
            }
          }
        }

        return [];
      }),
      catchError((e) => throwError(() => this.normalizeError(e)))
    );
  }

  private normalizeError(err: any): Error {
    if (err && typeof err === 'object') {
      if (err.reason) return new Error(err.reason);
      if (err.message) return new Error(err.message);
    }
    return new Error(JSON.stringify(err));
  }
}
