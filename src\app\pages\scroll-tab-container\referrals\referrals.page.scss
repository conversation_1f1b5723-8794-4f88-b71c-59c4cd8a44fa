// ------------------ refferal css

/* Referral Table Styles (based on lab-table) */
.referral-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  font-family: 'Inter', Arial, sans-serif;
}

.referral-table th,
.referral-table td {
  text-align: left;
  font-size: 14px;
}

.referral-table th {
  background: #D6E9FF;
  font-weight: 400;
  color: #374151;
  font-size: 12px;
  height: 33px;
}

.referral-table tr {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.referral-table tr:last-child {
  border-bottom: none;
}

.referral-table td {
  vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-right: 24px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  transition: color 0.2s;
}

.action-icons .icon:hover {
  color: #1976d2;
}

@media (max-width: 600px) {

  .referral-table th,
  .referral-table td {
    padding: 7px 4px;
    font-size: 12px;
  }
}

.referral-table th:nth-child(1),
.referral-table td:nth-child(1) {
  width: 300px;
  padding-left: 4px;
}

.referral-table th:nth-child(2),
.referral-table td:nth-child(2) {
  width: 867px;
}

.referral-table th:nth-child(3),
.referral-table td:nth-child(3) {
  width: 58px;
}

.referral-table tr:nth-child(even) {
  background-color: #F9FAFB;
}
