<ion-content [fullscreen]="true">
 <div class="patient-history-section">
                <h3>Patient History</h3>

                <div class="history-grid">
                  <div class="field-block">
                    <label>History of Present Illness <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area " class="custom-textarea"></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Personal History <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area " class="custom-placeholder-textarea"></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Past Medical or Surgical History <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Past Medical or Surgical History <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Family History <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Current And Recent Medications <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Medical Allergies <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Other Allergies Or Sensitivities <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Additional Notes <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Physical Examination <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>

                  <div class="field-block">
                    <label>Review Note <span style="color: red;"> *</span></label>
                    <ion-textarea placeholder="Text area "></ion-textarea>
                  </div>
                </div>
              </div>

</ion-content>
