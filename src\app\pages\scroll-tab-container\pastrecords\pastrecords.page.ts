import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';



@Component({
  selector: 'app-pastrecords',
  templateUrl: './pastrecords.page.html',
  styleUrls: ['./pastrecords.page.scss', '../tabs/tabs.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class PastrecordsPage implements OnInit {

  constructor() { }

  ngOnInit() {
  }
   pastRecords = [
  { date: '05th May,2025', time: '09:15 AM', details: 'General <PERSON> (1 Image)', attendedBy: 'Nr. <PERSON><PERSON><PERSON>' },
  { date: '05th May,2025', time: '09:30 AM', details: 'Dermatoscope (4 Images)', attendedBy: 'Nr. <PERSON><PERSON><PERSON>' },
  { date: '05th May,2025', time: '09:45 AM', details: 'ECG (1 Image)', attendedBy: 'Nr. <PERSON><PERSON><PERSON>' },
  { date: '05th May,2025', time: '10:00 AM', details: 'Report No. 124', attendedBy: 'Nr. <PERSON>lpa <PERSON>' },
  { date: '05th May,2025', time: '10:30 AM', details: 'Report No. 122', attendedBy: 'Nr. Shilpa Sharma' }
];

pageSizes = [5, 10, 25];
pageSize = 5;
currentPage = 1;
totalItems = 50;

get currentPageRange() {
  const start = (this.currentPage - 1) * this.pageSize + 1;
  const end = Math.min(this.currentPage * this.pageSize, this.totalItems);
  return `${start.toString().padStart(2, '0')}-${end.toString().padStart(2, '0')}`;
}

get totalPages() {
  return Math.ceil(this.totalItems / this.pageSize);
}

previousPage() {
  if (this.currentPage > 1) this.currentPage--;
}

nextPage() {
  if (this.currentPage < this.totalPages) this.currentPage++;
}

deleteRecord(record: any) {
  console.log('Delete record:', record);
}

removeFilter(type: string) {
  console.log(`Removing filter of type: ${type}`);
  // Add your actual logic to remove filter here
}

}
