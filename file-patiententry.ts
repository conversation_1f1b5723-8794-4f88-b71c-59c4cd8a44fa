import { HttpClient, HttpClientModule} from '@angular/common/http';
@Component({

  selector: 'app-patient-entry',
  templateUrl: './patient-entry.page.html',
  styleUrls: ['./patient-entry.page.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    IonicModule,
    CommonModule,
    HttpClientModule ,
    // IonRadioGroup,
  ],
})
export class PatientEntryPage implements OnInit {
   countryList: any[] = [];
  stateList: any[] = [];
  districtList: string[] = [];
  masterData: any = {};

 constructor(
    public objPouchdbService: PouchdbService,
    private fb: FormBuilder,
    private toastController: ToastController,
    private http: HttpClient
  )
 // Load master data on init
    await this.loadMasterData();


/** ✅ Load Master Data JSON into PouchDB (if not already saved) */
  private async loadMasterData() {
    try {
      this.objPouchdbService.getMasterData().subscribe({
        next: (data) => {
          console.log('✅ Master Data already exists in PouchDB');
          this.processMasterData(data);
        },
        error: () => {
          console.log('⚠ Master Data not found → Loading from assets...');
          this.http.get('assets/RemediNovaAPI.json').subscribe((jsonData: any) => {
            this.objPouchdbService.addOrUpdateMasterData(jsonData).subscribe(() => {
              console.log('✅ Master Data saved in PouchDB');
              this.processMasterData(jsonData);
            });
          });
        },
      });
    } catch (err) {
      console.error('❌ Error loading master data:', err);
    }
  }

  /** ✅ Process and extract master data from the JSON structure */
  private processMasterData(data: any) {
    try {
      console.log('🔍 Processing master data:', data);

      // Extract tables from the nested data structure
      if (data.data && Array.isArray(data.data)) {
        console.log('📊 Found data array with', data.data.length, 'items');

        // Find each table in the data array
        data.data.forEach((item: any, index: number) => {
          if (item.tblcountry) {
            this.masterData.tblcountry = item.tblcountry;
            console.log('🌍 Found tblcountry at index', index, 'with', item.tblcountry.length, 'countries');
          }


        });
      } else {
        // If data is already processed and stored directly
        console.log('📋 Using direct data structure');
        this.masterData = data;
      }

      // Set country list for dropdown (filter out deleted countries)
      if (this.masterData.tblcountry) {
        this.countryList = this.masterData.tblcountry.filter(
          (country: any) => country.IsDeleted === "0"
        );
        console.log('✅ Countries loaded:', this.countryList.length);
        console.log('🔍 Sample countries:', this.countryList.slice(0, 3));

        // Temporary alert to verify data loading
        if (this.countryList.length > 0) {
          console.log('🎉 SUCCESS: Countries dropdown should now work!');
        }
      } else {
        console.log('❌ No tblcountry found in master data');
        console.log('🔍 Available keys in masterData:', Object.keys(this.masterData));
      }

      console.log('✅ Master Data processed successfully');
      console.log('📊 Master data structure:', Object.keys(this.masterData));
    } catch (err) {
      console.error('❌ Error processing master data:', err);
    }
  }




}

onStateChange(event: any) {
  const selectedStateId = event.target.value;
  this.patientForm.patchValue({ district: '' });

  // ✅ Use already loaded masterData
  if (this.masterData.tbldistrict) {
    this.districtList = this.masterData.tbldistrict
      .filter((d: any) => d.StateId === selectedStateId && d.IsDeleted === "0")
      .map((d: any) => d.District);
    console.log('✅ Districts loaded for state:', selectedStateId, this.districtList.length);
  } else {
    this.districtList = [];
    console.log('⚠ No districts data available');
  }
}

