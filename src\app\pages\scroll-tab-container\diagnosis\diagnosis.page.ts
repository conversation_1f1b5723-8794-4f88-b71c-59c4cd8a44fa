import { Component, OnInit } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { IonicModule } from "@ionic/angular";
import { HttpClient, HttpClientModule } from "@angular/common/http";
import { PouchdbService } from "../../../service/pouchdb.service";

@Component({
  selector: "app-diagnosis",
  templateUrl: "./diagnosis.page.html",
  styleUrls: ["./diagnosis.page.scss"],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, HttpClientModule],
})
export class DiagnosisPage implements OnInit {
  masterData: any = {};
  diagnosisList: any[] = [];
  chapterList: any[] = [];
  diagnosisCategories: any[] = [];
 diagnosisChapters: any[] = [];

  diagnosisMode: "select" | "chapter" | "manual" = "select";
  selectedDiagnosis: string = "";
  isProvisional: boolean = false;

  diagnoses: { code: string; name: string; provisional: boolean }[] = [];

  constructor(private http: HttpClient, private objPouchdbService: PouchdbService) {}

  async ngOnInit() {
    console.log(' DiagnosisPage ngOnInit called');
    await this.loadMasterData();
  }

  /**  Load Master Data JSON into PouchDB (if not already saved) */
  private async loadMasterData() {
    try {
      console.log(' Loading master data...');
      this.objPouchdbService.getMasterData().subscribe({
        next: (data) => {
          console.log(' Master Data already exists in PouchDB', data);
          this.processMasterData(data);
        },
        error: (error) => {
          console.log(' Master Data not found → Loading from assets...', error);
          this.http.get('assets/data/RemediNovaAPI.json').subscribe({
            next: (jsonData: any) => {
              console.log(' Loaded JSON from assets:', jsonData);
              this.objPouchdbService.addOrUpdateMasterData(jsonData).subscribe({
                next: () => {
                  console.log(' Master Data saved in PouchDB');
                  this.processMasterData(jsonData);
                },
                error: (saveError) => {
                  console.error(' Error saving to PouchDB:', saveError);
                  // Still try to process the data even if saving fails
                  this.processMasterData(jsonData);
                }
              });
            },
            error: (httpError) => {
              console.error(' Error loading JSON from assets:', httpError);
            }
          });
        },
      });
    } catch (err) {
      console.error(' Error loading master data:', err);
    }
  }

  /** ✅ Extract tblDiagnosisCategory and tblDiagnosisChapter */
  private processMasterData(data: any) {
    this.masterData = data;

    console.log(' Processing master data:', data);
    console.log(' Data keys:', Object.keys(data));

    // Initialize arrays
    this.diagnosisList = [];
    this.chapterList = [];

    // Handle nested data structure - data is wrapped in a 'data' array
    if (data.data && Array.isArray(data.data)) {
      console.log('📦 Found data array with', data.data.length, 'items');
      // Find the diagnosis tables in the data array
      for (const item of data.data) {
        console.log('🔍 Checking item:', Object.keys(item));

        if (item.tbldiagnosiscategory) {
          this.diagnosisList = item.tbldiagnosiscategory.filter(
            (d: any) => d.IsDeleted === "0"
          );
          console.log('✅ Found diagnosis categories:', this.diagnosisList.length);
          console.log('📋 Sample category:', this.diagnosisList[0]);
        }

        if (item.tbldiagnosischapter) {
          this.chapterList = item.tbldiagnosischapter.filter(
            (c: any) => c.isdeleted === "0"
          );
          console.log('✅ Found diagnosis chapters:', this.chapterList.length);
          console.log('📋 Sample chapter:', this.chapterList[0]);
        }
      }
    } else {
      // Direct access (if data is already processed)
      console.log('📦 Direct data access');

      if (this.masterData.tbldiagnosiscategory) {
        this.diagnosisList = this.masterData.tbldiagnosiscategory.filter(
          (d: any) => d.IsDeleted === "0"
        );
        console.log('✅ Found diagnosis categories (direct):', this.diagnosisList.length);
      }

      if (this.masterData.tbldiagnosischapter) {
        this.chapterList = this.masterData.tbldiagnosischapter.filter(
          (c: any) => c.isdeleted === "0"
        );
        console.log('✅ Found diagnosis chapters (direct):', this.chapterList.length);
      }
    }

    console.log('📊 Final diagnosis lists:', {
      diagnosisList: this.diagnosisList.length,
      chapterList: this.chapterList.length
    });

    // Force change detection
    setTimeout(() => {
      console.log(' Triggering change detection');
    }, 100);
  }

  /**  Add Diagnosis */
  addDiagnosis() {
    if (!this.selectedDiagnosis.trim()) return;

    const newDiagnosis = {
      code: this.generateICDCode(),
      name: this.selectedDiagnosis,
      provisional: this.isProvisional,
    };

    this.diagnoses.push(newDiagnosis);
    this.selectedDiagnosis = "";
    this.isProvisional = false;
  }

  editDiagnosis(item: any) {
    this.selectedDiagnosis = item.name;
    this.isProvisional = item.provisional;
    this.deleteDiagnosis(item);
  }

  deleteDiagnosis(item: any) {
    this.diagnoses = this.diagnoses.filter((d) => d !== item);
  }

  generateICDCode(): string {
    return "X" + Math.floor(100 + Math.random() * 900); // Mock ICD Code
  }
}
