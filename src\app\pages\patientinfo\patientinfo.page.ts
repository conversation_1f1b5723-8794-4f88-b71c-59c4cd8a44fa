
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Component, OnInit, ChangeDetectorRef, NgZone } from '@angular/core';
import { SidebarPage } from '../sidebar/sidebar.page';
import { TabsPage } from '../scroll-tab-container/tabs/tabs.page';


@Component({
  selector: 'app-patientinfo',
  templateUrl: './patientinfo.page.html',
  styleUrls: ['./patientinfo.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, SidebarPage, TabsPage],
})
export class PatientinfoPage implements OnInit {
  ngOnInit(): void {
    // Initialization logic here
  }
}
