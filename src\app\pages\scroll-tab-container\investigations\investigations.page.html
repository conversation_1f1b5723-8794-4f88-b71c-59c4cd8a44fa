 <div class="diagnosis-card cards">
                <ion-card-header>
                  <div class=" ion-inherit-color">Investigations</div>
                </ion-card-header>
                <div>

                  <ion-label class="diagnosis-header">How would you like to add Investigations?</ion-label>

                  <div class="select-diagnosis">
                    <span class="diagnosis-radio-item">

                      <input type="radio" slot="start" value="select">
                      <label>Select Test</label>
                    </span>

                    <span class="diagnosis-radio-item">

                      <input type="radio" slot="start" value="chapter">
                      <label>Select Lab and Test</label>
                    </span>


                  </div>
                  <div class="diagnosis-input-group">
                    <div class="diagnosis-checkbox">
                      <ion-label>Test</ion-label>
                      <div>
                        <input type="checkbox"  style="margin-right: 10px;">
                        <ion-label>Provisional</ion-label>
                      </div>
                    </div>

                    <div class="dig-input-contain">

                      <input type="text" class="diagnosis-input"
                        placeholder="Select or Enter Test">

                      <span >+ Add</span>
                    </div>
                  </div>

                </div>
              </div>

              <!-- ---------- table  -->
              <table class="lab-table">
                <thead>
                  <tr>
                    <th>Lab</th>
                    <th>Test</th>
                    <th>Mandatory</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Not Available</td>
                    <td>Clotting Time</td>
                    <td>Yes</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>ABC Diagnostic Center</td>
                    <td>Complete Blood Count (CBC)</td>
                    <td>Yes</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Apollo Clinic</td>
                    <td>Urine Test</td>
                    <td>No</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>Clinical Biochemistry</td>
                    <td>Prothrombin Time</td>
                    <td>No</td>
                    <td>
                      <span class="action-icons">
                        <span class="icon" title="Edit"><img src="assets/icon/edit.png" alt=""></span>
                        <span class="icon" title="Delete"><img src="assets/icon/delete.png" alt=""></span>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
