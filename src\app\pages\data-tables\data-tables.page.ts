import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RemedinovaDataService } from '../../services/remedinova-data.service';
import { DataLoaderService } from '../../services/data-loader.service';
import { PouchService, TableConfig } from '../../services/pouch.service';

interface TableData {
  name: string;
  displayName: string;
  data: any[];
  columns: string[];
  totalCount: number;
  isLoading: boolean;
  isExpanded: boolean;
  searchText: string;
  filteredData: any[];
}

@Component({
  selector: 'app-data-tables',
  templateUrl: './data-tables.page.html',
  styleUrls: ['./data-tables.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class DataTablesPage implements OnInit {

  tables: TableData[] = [];
  isInitialLoading = false;
  totalDocuments = 0;
  selectedTable: string | null = null;

  // Table display names mapping
  private tableDisplayNames: { [key: string]: string } = {
    'locationhierarchy': 'Location Hierarchy',
    'tblpatientcomplaints': 'Patient Complaints',
    'tblstate': 'States',
    'tblcountry': 'Countries',
    'tbldistrict': 'Districts',
    'tblblock': 'Blocks',
    'tblvillage': 'Villages',
    'tblspeciality_list': 'Medical Specialties',
    'tblreferralspecialtylist': 'Referral Specialties',
    'tblinstructions': 'Medical Instructions',
    'tbllabcategory': 'Lab Categories',
    'tbllabsubtest': 'Lab Sub Tests',
    'tblspecialinstruction': 'Special Instructions',
    'tblmedicinemaster': 'Medicine Master',
    'tblmedicationbrand': 'Medication Brands',
    'tbldrug_class': 'Drug Classes',
    'tbldrug_form': 'Drug Forms',
    'tbldiagnosiscategory': 'Diagnosis Categories',
    'tbldiagnosischapter': 'Diagnosis Chapters',
    'tbldiagnosismaster': 'Diagnosis Master',
    'tbldiagnosissubchapter': 'Diagnosis Sub Chapters'
  };

  constructor(
    private remedinovaData: RemedinovaDataService,
    private dataLoader: DataLoaderService,
    private pouchService: PouchService
  ) {}

  async ngOnInit() {
    await this.initializeTables();
  }

  async initializeTables() {
    this.isInitialLoading = true;

    try {
      // Ensure data is loaded
      await this.dataLoader.ensureDataLoaded();

      // Get available tables
      const availableTables = this.pouchService.getAvailableTables();

      // Initialize table data structure
      this.tables = availableTables.map(config => ({
        name: config.name,
        displayName: this.tableDisplayNames[config.name] || config.name,
        data: [],
        columns: [],
        totalCount: 0,
        isLoading: false,
        isExpanded: false,
        searchText: '',
        filteredData: []
      }));

      // Get database statistics
      const stats = await this.remedinovaData.getDatabaseStats();
      this.totalDocuments = Object.values(stats).reduce(
        (total: number, info: any) => total + (info.doc_count || 0),
        0
      );

      // Update table counts
      this.tables.forEach(table => {
        const tableStats = stats[table.name];
        if (tableStats) {
          table.totalCount = tableStats.doc_count || 0;
        }
      });

      // Sort tables by document count (descending)
      this.tables.sort((a, b) => b.totalCount - a.totalCount);

    } catch (error) {
      console.error('Error initializing tables:', error);
    } finally {
      this.isInitialLoading = false;
    }
  }

  async loadTableData(table: TableData) {
    if (table.data.length > 0) {
      table.isExpanded = !table.isExpanded;
      return;
    }

    table.isLoading = true;

    try {
      // Load all data for the table
      const data = await this.pouchService.getAllFromTable(table.name);
      table.data = data;
      table.filteredData = data;

      // Extract column names from first few records
      if (data.length > 0) {
        const sampleRecords = data.slice(0, 5);
        const allKeys = new Set<string>();

        sampleRecords.forEach(record => {
          Object.keys(record).forEach(key => {
            if (!key.startsWith('_')) { // Exclude PouchDB internal fields
              allKeys.add(key);
            }
          });
        });

        table.columns = Array.from(allKeys).sort();
      }

      table.isExpanded = true;

    } catch (error) {
      console.error(`Error loading data for ${table.name}:`, error);
    } finally {
      table.isLoading = false;
    }
  }

  onTableSearch(table: TableData) {
    if (!table.searchText.trim()) {
      table.filteredData = table.data;
      return;
    }

    const searchTerm = table.searchText.toLowerCase();
    table.filteredData = table.data.filter(record => {
      return Object.values(record).some(value => {
        if (value === null || value === undefined) return false;
        return value.toString().toLowerCase().includes(searchTerm);
      });
    });
  }

  toggleTable(table: TableData) {
    if (table.isExpanded && table.data.length > 0) {
      table.isExpanded = false;
    } else {
      this.loadTableData(table);
    }
  }

  getDisplayValue(value: any): string {
    if (value === null || value === undefined) {
      return '-';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return value.toString();
  }

  getTableIcon(tableName: string): string {
    const iconMap: { [key: string]: string } = {
      'tblpatientcomplaints': 'medical-outline',
      'tblmedicinemaster': 'flask-outline',
      'tbldiagnosismaster': 'clipboard-outline',
      'tblstate': 'location-outline',
      'tblcountry': 'globe-outline',
      'tbldistrict': 'map-outline',
      'tblvillage': 'home-outline',
      'tbllabsubtest': 'beaker-outline',
      'tblspeciality_list': 'school-outline',
      'tblinstructions': 'document-text-outline',
      'locationhierarchy': 'layers-outline'
    };
    return iconMap[tableName] || 'list-outline';
  }

  async exportTableData(table: TableData) {
    if (table.data.length === 0) {
      await this.loadTableData(table);
    }

    const dataStr = JSON.stringify(table.data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `${table.name}_data.json`;
    link.click();

    URL.revokeObjectURL(url);
  }

  async loadAllTables() {
    for (const table of this.tables) {
      if (table.data.length === 0) {
        await this.loadTableData(table);
        // Small delay to prevent UI blocking
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }

  getLoadedTablesCount(): number {
    return this.tables.filter(table => table.data.length > 0).length;
  }

  getTotalLoadedRecords(): number {
    return this.tables.reduce((total, table) => total + table.data.length, 0);
  }

  trackByTableName(index: number, table: TableData): string {
    return table.name;
  }
}
