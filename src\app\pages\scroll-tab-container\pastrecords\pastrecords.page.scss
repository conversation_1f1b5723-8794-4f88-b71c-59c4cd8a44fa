// / ---------------- past record-css -------------------- /
//table css
/* New Table Styles */
.new-past-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  font-family: 'Inter', <PERSON><PERSON>, sans-serif;
}

.new-past-table th,
.new-past-table td {
  text-align: left;
  font-size: 14px;
}

.new-past-table th {
  background: #D6E9FF;
  font-weight: 400;
  color: #374151;
  font-size: 12px;
  height: 33px;
}

.new-past-table tr {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.new-past-table tr:last-child {
  border-bottom: none;
}

.new-past-table td {
  vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-right: 24px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  transition: color 0.2s;
}

.action-icons .icon:hover {
  color: #1976d2;
}

@media (max-width: 600px) {

  .new-past-table th,
  .new-past-table td {
    padding: 7px 4px;
    font-size: 12px;
  }
}

.new-past-table th:nth-child(1),
.new-past-table td:nth-child(1) {
  width: 120px;
  padding-left: 4px;
}

.new-past-table th:nth-child(2),
.new-past-table td:nth-child(2) {
  width: 80px;
}

.new-past-table th:nth-child(3),
.new-past-table td:nth-child(3) {
  width: 785px;
}

.new-past-table th:nth-child(4),
.new-past-table td:nth-child(4) {
  width: 150px;
}
.new-past-table th:nth-child(5),
.new-past-table td:nth-child(5) {
  width: 58px;
}

.new-past-table tr:nth-child(even) {
  background-color: #F9FAFB;
}

  //button css
  ion-row {
    background-color: #F9FAFB;
    padding: 7px 0;
    border-bottom: 1px solid #eee;
    align-items: center;

    &:last-child {
      border-bottom: none;
    }
  }

  .trash-icon {
    font-size: 18px;
    color: #999;
    cursor: pointer;

    &:hover {
      color: red;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    gap: 15px;

    select {
      width: 97px;
      background: transparent;
      border: 1px solid #D1D5DB;
      border-radius: 9px;
      font-size: 12px;
      font-weight: 500;
      font-family: "DM Sans", sans-serif;
      color: #4A4A48;
      padding: 11px 7px;
    }

    .page-btn {
      display: flex;
      align-items: center;
      width: 209px;
      height: 45px;
      background: transparent;
      border: 1px solid #D1D5DB;
      border-radius: 9px;
      font-size: 12px;
      font-weight: 500;
      font-family: "DM Sans", sans-serif;
      color: #4A4A48;
      padding: 0px 8px 0 8px;
    }

    .page-info {
      font-size: 14px;
      color: #444;
    }
  }
