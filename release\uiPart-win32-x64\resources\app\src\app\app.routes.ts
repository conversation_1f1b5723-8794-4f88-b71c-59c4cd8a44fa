import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'home',
    loadComponent: () => import('./home/<USER>').then((m) => m.HomePage),
  },
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },
  {
    path: 'navbar',
    loadComponent: () => import('./pages/navbar/navbar.page').then( m => m.NavbarPage)
  },
  {
    path: 'navbar',
    loadComponent: () => import('./pages/navbar/navbar.page').then( m => m.NavbarPage)
  },
  {
    path: 'sidebar',
    loadComponent: () => import('./pages/sidebar/sidebar.page').then( m => m.SidebarPage)
  },
  {
    path: 'patientinfo',
    loadComponent: () => import('./pages/patientinfo/patientinfo.page').then( m => m.PatientinfoPage)
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./pages/dashboard/dashboard.page').then( m => m.DashboardPage)
  },

  {
    path: 'complaints',
    loadComponent: () => import('./pages/complaints/complaints.page').then( m => m.ComplaintsPage)
  },
  {
    path: 'past-records',
    loadComponent: () => import('./pages/past-records/past-records.page').then( m => m.PastRecordsPage)
  },
  {
    path: 'parameters',
    loadComponent: () => import('./pages/parameters/parameters.page').then( m => m.ParametersPage)
  },
  {
    path: 'dignosis',
    loadComponent: () => import('./pages/dignosis/dignosis.page').then( m => m.DignosisPage)
  },
  

  {
    path: 'intervention',
    loadComponent: () => import('./pages/intervention/intervention.page').then( m => m.InterventionPage)
  },
  {
    path: 'investigations',
    loadComponent: () => import('./pages/investigations/investigations.page').then( m => m.InvestigationsPage)
  },
  {
    path: 'counseling',
    loadComponent: () => import('./pages/counseling/counseling.page').then( m => m.CounselingPage)
  },
  {
    path: 'referrals',
    loadComponent: () => import('./pages/referrals/referrals.page').then( m => m.ReferralsPage)
  },
];
