{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"app": {"projectType": "application", "schematics": {"@ionic/angular-toolkit:page": {"styleext": "scss", "standalone": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "www", "browser": ""}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["node_modules/@ionic/angular/css/core.css", "node_modules/@ionic/angular/css/normalize.css", "node_modules/@ionic/angular/css/structure.css", "node_modules/@ionic/angular/css/typography.css", "node_modules/@ionic/angular/css/display.css", "src/global.scss", "src/theme/variables.scss", "src/electron-fixes.scss"], "scripts": ["src/electron-scroll-fix.js"], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "25kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "ci": {"progress": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "app:build:production"}, "development": {"buildTarget": "app:build:development"}, "ci": {"progress": false}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["src/global.scss", "src/theme/variables.scss"], "scripts": []}, "configurations": {"ci": {"progress": false, "watch": false}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@ionic/angular-toolkit"], "analytics": false}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}, "@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}