const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    show: true
  });

  mainWindow.webContents.openDevTools();
  
  // Load the simple test first
  mainWindow.loadFile('test-tabs-simple.html').then(() => {
    console.log('Simple tab test loaded');
    
    // After 5 seconds, load the actual app
    setTimeout(() => {
      console.log('Loading actual app...');
      mainWindow.loadFile(path.join(__dirname, 'www/index.html')).then(() => {
        console.log('App loaded');
        
        // Check for tabs after app loads
        setTimeout(() => {
          mainWindow.webContents.executeJavaScript(`
            console.log('=== CHECKING ACTUAL APP TABS ===');
            
            // Check if PatientinfoPage component is loaded
            const patientInfo = document.querySelector('app-patientinfo');
            console.log('app-patientinfo found:', !!patientInfo);
            
            // Check for tab wrapper
            const tabWrapper = document.querySelector('.scroll-tabs-wrapper');
            console.log('Tab wrapper found:', !!tabWrapper);
            
            // Check for ion-segment
            const segment = document.querySelector('ion-segment[scrollable]');
            console.log('Scrollable segment found:', !!segment);
            
            // Check for segment buttons
            const buttons = document.querySelectorAll('ion-segment-button');
            console.log('Segment buttons found:', buttons.length);
            
            if (buttons.length > 0) {
              buttons.forEach((button, index) => {
                console.log('Button', index, ':', {
                  textContent: button.textContent.trim(),
                  innerHTML: button.innerHTML,
                  display: getComputedStyle(button).display,
                  opacity: getComputedStyle(button).opacity,
                  visibility: getComputedStyle(button).visibility,
                  color: getComputedStyle(button).color,
                  fontSize: getComputedStyle(button).fontSize
                });
              });
            }
            
            console.log('=== END APP TAB CHECK ===');
          `);
        }, 3000);
        
      }).catch(err => {
        console.error('Failed to load app:', err);
      });
    }, 5000);
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
