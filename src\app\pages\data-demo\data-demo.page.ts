import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RemedinovaDataService, PatientComplaint, Medicine, Diagnosis } from '../../services/remedinova-data.service';
import { DataLoaderService } from '../../services/data-loader.service';

@Component({
  selector: 'app-data-demo',
  templateUrl: './data-demo.page.html',
  styleUrls: ['./data-demo.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class DataDemoPage implements OnInit {

  // Data arrays
  complaints: PatientComplaint[] = [];
  medicines: Medicine[] = [];
  diagnoses: Diagnosis[] = [];
  states: any[] = [];
  countries: any[] = [];

  // Search functionality
  searchText = '';
  searchResults: any[] = [];
  selectedTable = 'complaints';

  // Loading states
  isLoading = false;
  isDataLoaded = false;

  // Statistics
  dbStats: any = {};

  constructor(
    private remedinovaData: RemedinovaDataService,
    private dataLoader: DataLoaderService
  ) {}

  async ngOnInit() {
    await this.loadInitialData();
  }

  async loadInitialData() {
    this.isLoading = true;
    try {
      // Ensure data is loaded
      await this.dataLoader.ensureDataLoaded();
      this.isDataLoaded = true;

      // Load some sample data
      await this.loadSampleData();

      // Get database statistics
      this.dbStats = await this.remedinovaData.getDatabaseStats();

    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  async loadSampleData() {
    try {
      // Load first 10 items from each table for demo
      const [complaints, medicines, diagnoses, states, countries] = await Promise.all([
        this.remedinovaData.getAllComplaints(),
        this.remedinovaData.getAllMedicines(),
        this.remedinovaData.getAllDiagnoses(),
        this.remedinovaData.getAllStates(),
        this.remedinovaData.getAllCountries()
      ]);

      this.complaints = complaints.slice(0, 10);
      this.medicines = medicines.slice(0, 10);
      this.diagnoses = diagnoses.slice(0, 10);
      this.states = states.slice(0, 10);
      this.countries = countries.slice(0, 10);

    } catch (error) {
      console.error('Error loading sample data:', error);
    }
  }

  async onSearch() {
    if (!this.searchText.trim()) {
      this.searchResults = [];
      return;
    }

    this.isLoading = true;
    try {
      switch (this.selectedTable) {
        case 'complaints':
          this.searchResults = await this.remedinovaData.searchComplaints(this.searchText);
          break;
        case 'medicines':
          this.searchResults = await this.remedinovaData.searchMedicines(this.searchText);
          break;
        case 'diagnoses':
          this.searchResults = await this.remedinovaData.searchDiagnoses(this.searchText);
          break;
        case 'instructions':
          this.searchResults = await this.remedinovaData.searchInstructions(this.searchText);
          break;
        case 'labtests':
          this.searchResults = await this.remedinovaData.searchLabTests(this.searchText);
          break;
        default:
          this.searchResults = [];
      }
    } catch (error) {
      console.error('Search error:', error);
      this.searchResults = [];
    } finally {
      this.isLoading = false;
    }
  }

  async onTableChange() {
    this.searchText = '';
    this.searchResults = [];
  }

  async reloadAllData() {
    this.isLoading = true;
    try {
      await this.dataLoader.reloadData();
      await this.loadSampleData();
      this.dbStats = await this.remedinovaData.getDatabaseStats();
    } catch (error) {
      console.error('Error reloading data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  getTableDisplayName(tableName: string): string {
    const displayNames: { [key: string]: string } = {
      'complaints': 'Patient Complaints',
      'medicines': 'Medicines',
      'diagnoses': 'Diagnoses',
      'instructions': 'Instructions',
      'labtests': 'Lab Tests'
    };
    return displayNames[tableName] || tableName;
  }

  getSearchResultDisplayText(item: any): string {
    switch (this.selectedTable) {
      case 'complaints':
        return item.ComplaintName || 'Unknown Complaint';
      case 'medicines':
        return item.Medicine_Name || 'Unknown Medicine';
      case 'diagnoses':
        return item.Diagnosis_Name || 'Unknown Diagnosis';
      case 'instructions':
        return item.instruction || 'Unknown Instruction';
      case 'labtests':
        return item.TestName || 'Unknown Test';
      default:
        return JSON.stringify(item);
    }
  }

  getItemCount(tableName: string): number {
    if (!this.dbStats[tableName]) return 0;
    return this.dbStats[tableName].doc_count || 0;
  }

  getTotalDocuments(): number {
    return Object.values(this.dbStats).reduce((total: number, info: any) => {
      return total + (info.doc_count || 0);
    }, 0);
  }

  getDbStatsKeys(): string[] {
    return Object.keys(this.dbStats);
  }
}
