import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-error-boundary',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div *ngIf="hasError" class="error-boundary">
      <ion-card color="danger">
        <ion-card-header>
          <ion-card-title>
            <ion-icon name="warning-outline"></ion-icon>
            Something went wrong
          </ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <p>{{ errorMessage }}</p>
          <div class="error-details" *ngIf="showDetails">
            <h4>Error Details:</h4>
            <pre>{{ errorStack }}</pre>
          </div>
          <div class="error-actions">
            <ion-button (click)="retry()" color="light">
              <ion-icon name="refresh-outline" slot="start"></ion-icon>
              Retry
            </ion-button>
            <ion-button (click)="toggleDetails()" fill="outline" color="light">
              {{ showDetails ? 'Hide' : 'Show' }} Details
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
    <ng-content *ngIf="!hasError"></ng-content>
  `,
  styles: [`
    .error-boundary {
      padding: 1rem;
    }
    
    .error-details {
      margin: 1rem 0;
      
      h4 {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }
      
      pre {
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        overflow-x: auto;
        white-space: pre-wrap;
      }
    }
    
    .error-actions {
      display: flex;
      gap: 0.5rem;
      margin-top: 1rem;
    }
    
    ion-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  `]
})
export class ErrorBoundaryComponent implements OnInit {
  @Input() errorMessage = 'An unexpected error occurred';
  
  hasError = false;
  showDetails = false;
  errorStack = '';

  ngOnInit() {
    // Set up global error handler
    window.addEventListener('error', (event) => {
      this.handleError(event.error || new Error(event.message));
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(new Error(event.reason));
    });
  }

  handleError(error: Error) {
    console.error('Error caught by boundary:', error);
    this.hasError = true;
    this.errorMessage = error.message || 'An unexpected error occurred';
    this.errorStack = error.stack || 'No stack trace available';
  }

  retry() {
    this.hasError = false;
    this.showDetails = false;
    this.errorStack = '';
    // Reload the page or reset the component state
    window.location.reload();
  }

  toggleDetails() {
    this.showDetails = !this.showDetails;
  }
}
