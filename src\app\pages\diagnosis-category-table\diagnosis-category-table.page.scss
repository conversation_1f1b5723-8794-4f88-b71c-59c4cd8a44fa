// Loading container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  
  ion-spinner {
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
}

// Stats card
.stats-card {
  margin: 1rem;
  background: linear-gradient(135deg, var(--ion-color-primary-tint), var(--ion-color-secondary-tint));
  color: white;
  
  ion-card-header {
    ion-card-title {
      display: flex;
      align-items: center;
      font-size: 1.2rem;
      font-weight: 600;
      
      .title-icon {
        font-size: 1.4rem;
        margin-right: 0.5rem;
      }
    }
    
    ion-card-subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 0.9rem;
    }
  }
  
  .stat-item {
    text-align: center;
    padding: 0.5rem;
    
    h2 {
      font-size: 1.8rem;
      font-weight: 700;
      margin: 0 0 0.25rem 0;
    }
    
    p {
      font-size: 0.8rem;
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      opacity: 0.9;
    }
  }
}

// Search card
.search-card {
  margin: 1rem;
  
  .search-info {
    margin-top: 0.5rem;
    
    ion-chip {
      --background: rgba(var(--ion-color-primary-rgb), 0.1);
      --color: var(--ion-color-primary);
    }
  }
}

// Table card
.table-card {
  margin: 1rem;
  
  .table-content {
    padding: 0;
  }
}

// Data table styles
.table-wrapper {
  overflow-x: auto;
  
  .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    
    thead {
      background: var(--ion-color-light);
      position: sticky;
      top: 0;
      z-index: 10;
      
      th {
        padding: 1rem 0.75rem;
        text-align: left;
        font-weight: 600;
        color: var(--ion-color-dark);
        border-bottom: 2px solid var(--ion-color-primary);
        white-space: nowrap;
        
        &.row-number {
          width: 60px;
          text-align: center;
        }
      }
    }
    
    tbody {
      tr {
        border-bottom: 1px solid var(--ion-color-light);
        transition: background-color 0.2s ease;
        
        &:hover {
          background-color: var(--ion-color-light-tint);
        }
        
        &:last-child {
          border-bottom: none;
        }
      }
      
      td {
        padding: 0.75rem;
        vertical-align: middle;
        
        &.row-number {
          text-align: center;
          font-weight: 500;
          color: var(--ion-color-medium);
          background-color: var(--ion-color-light-tint);
          width: 60px;
        }
        
        &.category-id {
          font-family: 'Courier New', monospace;
        }
        
        &.category-name {
          strong {
            color: var(--ion-color-dark);
            font-weight: 500;
          }
        }
        
        &.status {
          text-align: center;
        }
        
        &.domain {
          ion-chip {
            --background: rgba(var(--ion-color-secondary-rgb), 0.1);
            --color: var(--ion-color-secondary);
            font-size: 0.75rem;
          }
        }
      }
    }
  }
}

// No results state
.no-results {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--ion-color-medium);
  
  .no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }
  
  h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: var(--ion-color-dark);
  }
  
  p {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1.5rem;
  }
}

// Action buttons
.action-buttons {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  
  ion-button {
    --border-radius: 8px;
    height: 48px;
  }
}

// Footer info
.footer-info {
  margin: 1rem;
  background: var(--ion-color-light-tint);
  
  .footer-content {
    display: flex;
    align-items: flex-start;
    
    .info-icon {
      font-size: 1.5rem;
      color: var(--ion-color-primary);
      margin-right: 1rem;
      margin-top: 0.25rem;
      flex-shrink: 0;
    }
    
    .info-text {
      flex: 1;
      
      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--ion-color-primary);
        margin: 0 0 0.5rem 0;
      }
      
      p {
        font-size: 0.85rem;
        color: var(--ion-color-medium);
        line-height: 1.5;
        margin: 0 0 0.5rem 0;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        small {
          font-size: 0.75rem;
          opacity: 0.8;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .stats-card,
  .search-card,
  .table-card,
  .footer-info {
    margin: 0.5rem;
  }
  
  .stats-card {
    .stat-item {
      padding: 0.25rem;
      
      h2 {
        font-size: 1.4rem;
      }
      
      p {
        font-size: 0.75rem;
      }
    }
  }
  
  .data-table {
    font-size: 0.8rem;
    
    thead th {
      padding: 0.75rem 0.5rem;
    }
    
    tbody td {
      padding: 0.5rem;
      
      &.category-name strong {
        font-size: 0.85rem;
      }
    }
  }
  
  .action-buttons {
    padding: 0.5rem;
  }
  
  .footer-info {
    .footer-content {
      .info-icon {
        font-size: 1.3rem;
        margin-right: 0.75rem;
      }
      
      .info-text {
        h4 {
          font-size: 0.9rem;
        }
        
        p {
          font-size: 0.8rem;
        }
      }
    }
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .stats-card {
    background: linear-gradient(135deg, var(--ion-color-primary-shade), var(--ion-color-secondary-shade));
  }
  
  .data-table {
    thead {
      background-color: var(--ion-color-dark);
      
      th {
        color: var(--ion-color-light);
        border-bottom-color: var(--ion-color-primary-shade);
      }
    }
    
    tbody {
      tr:hover {
        background-color: var(--ion-color-dark-tint);
      }
      
      td.row-number {
        background-color: var(--ion-color-dark-tint);
      }
    }
  }
  
  .footer-info {
    background: var(--ion-color-dark-tint);
  }
}
