const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    },
    show: true
  });

  mainWindow.webContents.openDevTools();
  
  mainWindow.loadFile(path.join(__dirname, 'www/index.html'));

  mainWindow.webContents.on('dom-ready', () => {
    console.log('DOM ready, checking for tabs...');
    
    // Wait for Angular to load, then check tabs
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        console.log('=== CHECKING HORIZONTAL TABS ===');
        
        // Check for tab wrapper
        const tabWrapper = document.querySelector('.scroll-tabs-wrapper');
        console.log('Tab wrapper found:', !!tabWrapper);
        if (tabWrapper) {
          console.log('Tab wrapper styles:', {
            display: getComputedStyle(tabWrapper).display,
            overflowX: getComputedStyle(tabWrapper).overflowX,
            overflowY: getComputedStyle(tabWrapper).overflowY,
            width: getComputedStyle(tabWrapper).width,
            opacity: getComputedStyle(tabWrapper).opacity,
            visibility: getComputedStyle(tabWrapper).visibility
          });
        }
        
        // Check for ion-segment
        const segment = document.querySelector('ion-segment[scrollable]');
        console.log('Scrollable segment found:', !!segment);
        if (segment) {
          console.log('Segment styles:', {
            display: getComputedStyle(segment).display,
            overflowX: getComputedStyle(segment).overflowX,
            width: getComputedStyle(segment).width,
            minWidth: getComputedStyle(segment).minWidth,
            opacity: getComputedStyle(segment).opacity,
            visibility: getComputedStyle(segment).visibility
          });
        }
        
        // Check for segment buttons
        const buttons = document.querySelectorAll('ion-segment-button');
        console.log('Segment buttons found:', buttons.length);
        if (buttons.length > 0) {
          console.log('First button styles:', {
            display: getComputedStyle(buttons[0]).display,
            width: getComputedStyle(buttons[0]).width,
            minWidth: getComputedStyle(buttons[0]).minWidth,
            opacity: getComputedStyle(buttons[0]).opacity,
            visibility: getComputedStyle(buttons[0]).visibility
          });
          
          // List all tab labels
          const labels = Array.from(buttons).map(btn => btn.textContent.trim());
          console.log('Tab labels:', labels);
        }
        
        console.log('=== END TAB CHECK ===');
      `);
    }, 3000);
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
