 <div class="intervention-card cards">
                <ion-card-header>
                  <div class=" ion-inherit-color">Intervention</div>
                </ion-card-header>

                <div>
                  <div class="intervention-grid">

                    <div class="intervention-group">
                      <label class="intervention-label">Treatment Plan</label>
                      <div class="plan-container">
                        <textarea class="plan-container" placeholder="Type the treatment plan *"
                          [(ngModel)]="intervention.treatmentPlan" class="intervention-textarea med-input"></textarea>
                      </div>
                    </div>

                    <div class="intervention-group">
                      <label class="intervention-label">Clinical Observations</label>
                      <div class="plan-container">
                        <textarea type="text" placeholder="Type the Clinical Observations *"
                          [(ngModel)]="intervention.clinicalObservations"
                          class="intervention-textarea med-input"></textarea>
                      </div>
                    </div>

                  </div>

                  <div class="intervention-followup">
                    <label class="intervention-label">Follow-Up Schedule</label>
                    <input type="date" [(ngModel)]="intervention.followUpDate"
                      class="intervention-date native-date-input med-input" style="margin-top: 14px;" />
                  </div>

                </div>
              </div>
