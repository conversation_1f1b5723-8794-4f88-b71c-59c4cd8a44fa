import { Component, OnInit } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { TableNavigatorPage } from '../pages/table-navigator/table-navigator.page';
import { StartupService, StartupResult } from '../services/startup.service';
import { DataTestService } from '../services/data-test.service';
import { DiagnosisCategoryTablePage } from '../pages/diagnosis-category-table/diagnosis-category-table.page';

@Component({
  standalone: true,
  selector: 'app-home',
  templateUrl: './home.page.html',
  styleUrls: ['./home.page.scss'],
  imports: [
    IonicModule,
    CommonModule,
    TableNavigatorPage,
    DiagnosisCategoryTablePage
  ],
})
export class HomePage implements OnInit {
  isDataLoading = true;
  dataLoadError = false;
  errorMessage = '';
  diagnosticInfo: any = null;
  startupResult: StartupResult | null = null;

  constructor(
    private startupService: StartupService,
    private dataTest: DataTestService
  ) {}

  async ngOnInit() {
    try {
      console.log('🚀 Starting RemediNova application...');

      // Run comprehensive startup process
      this.startupResult = await this.startupService.initialize();

      if (this.startupResult.success) {
        this.diagnosticInfo = this.startupResult.details.diagnostics;
        console.log('✅ Application started successfully');
        this.isDataLoading = false;
      } else {
        throw new Error(this.startupResult.message);
      }

    } catch (error: any) {
      console.error('❌ Failed to start application:', error);
      this.dataLoadError = true;
      this.errorMessage = error.message || 'Failed to initialize application';
      this.isDataLoading = false;

      // Extract diagnostic info even on error
      if (this.startupResult?.details?.diagnostics) {
        this.diagnosticInfo = this.startupResult.details.diagnostics;
      }

      // Provide more specific error information
      this.categorizeError(error.message);
    }
  }

  private categorizeError(errorMessage: string) {
    if (errorMessage.includes('fetch') || errorMessage.includes('RemediNovaAPI.json')) {
      this.errorMessage = 'Cannot access data file. Please ensure RemediNovaAPI.json exists in src/assets/data/';
    } else if (errorMessage.includes('PouchDB') || errorMessage.includes('IndexedDB')) {
      this.errorMessage = 'Database initialization failed. Your browser may not support IndexedDB or it may be disabled.';
    } else if (errorMessage.includes('network') || errorMessage.includes('offline')) {
      this.errorMessage = 'Network error. Please check your internet connection and try again.';
    } else {
      this.errorMessage = errorMessage;
    }
  }

  async retryDataLoad() {
    this.isDataLoading = true;
    this.dataLoadError = false;
    this.errorMessage = '';
    this.diagnosticInfo = null;
    this.startupResult = null;
    await this.ngOnInit();
  }

  async runQuickTest() {
    try {
      console.log('🧪 Running quick test...');
      const result = await this.dataTest.quickTest();
      if (result) {
        console.log('✅ Quick test passed');
        alert('Quick test passed! The database is working correctly.');
      } else {
        console.error('❌ Quick test failed');
        alert('Quick test failed. Check the browser console for details.');
      }
    } catch (error) {
      console.error('❌ Quick test error:', error);
      alert(`Quick test error: ${error}`);
    }
  }

  getErrorDetails(): string[] {
    if (!this.startupResult) return [];
    return [...this.startupResult.errors, ...this.startupResult.warnings];
  }

  hasWarnings(): boolean {
    return (this.startupResult?.warnings?.length || 0) > 0;
  }
}
