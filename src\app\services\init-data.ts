/**
 * Data initialization utility
 * Use this to initialize RemediNova data in your application
 */

import { DataLoaderService } from './data-loader.service';
import { RemedinovaDataService } from './remedinova-data.service';
import { PouchService } from './pouch.service';

export class DataInitializer {
  
  constructor(
    private dataLoader: DataLoaderService,
    private remedinovaData: RemedinovaDataService,
    private pouchService: PouchService
  ) {}

  /**
   * Initialize all data and return statistics
   */
  async initializeWithStats(): Promise<{
    success: boolean;
    loadTime: number;
    tableStats: { [tableName: string]: PouchDB.Core.DatabaseInfo };
    totalDocuments: number;
    error?: string;
  }> {
    const startTime = Date.now();
    
    try {
      console.log('🚀 Starting RemediNova data initialization...');
      
      // Load all data
      await this.dataLoader.ensureDataLoaded();
      
      // Get statistics
      const tableStats = await this.remedinovaData.getDatabaseStats();
      const totalDocuments = Object.values(tableStats).reduce(
        (total: number, info: any) => total + (info.doc_count || 0), 
        0
      );
      
      const loadTime = Date.now() - startTime;
      
      console.log('✅ Data initialization completed successfully!');
      console.log(`📊 Total documents: ${totalDocuments}`);
      console.log(`⏱️ Load time: ${loadTime}ms`);
      
      return {
        success: true,
        loadTime,
        tableStats,
        totalDocuments
      };
      
    } catch (error: any) {
      const loadTime = Date.now() - startTime;
      console.error('❌ Data initialization failed:', error);
      
      return {
        success: false,
        loadTime,
        tableStats: {},
        totalDocuments: 0,
        error: error.message || 'Unknown error'
      };
    }
  }

  /**
   * Clear all data and reinitialize
   */
  async resetAllData(): Promise<void> {
    console.log('🔄 Resetting all RemediNova data...');
    
    try {
      await this.remedinovaData.clearAllData();
      console.log('🗑️ All data cleared');
      
      await this.dataLoader.reloadData();
      console.log('✅ Data reloaded successfully');
      
    } catch (error) {
      console.error('❌ Failed to reset data:', error);
      throw error;
    }
  }

  /**
   * Validate data integrity
   */
  async validateData(): Promise<{
    isValid: boolean;
    issues: string[];
    tableValidation: { [tableName: string]: { count: number; hasData: boolean } };
  }> {
    console.log('🔍 Validating data integrity...');
    
    const issues: string[] = [];
    const tableValidation: { [tableName: string]: { count: number; hasData: boolean } } = {};
    
    try {
      const stats = await this.remedinovaData.getDatabaseStats();
      const availableTables = this.pouchService.getAvailableTables();
      
      // Check each table
      for (const tableConfig of availableTables) {
        const tableName = tableConfig.name;
        const tableInfo = stats[tableName];
        
        if (!tableInfo) {
          issues.push(`Table '${tableName}' not found in database`);
          tableValidation[tableName] = { count: 0, hasData: false };
          continue;
        }
        
        const count = tableInfo.doc_count || 0;
        const hasData = count > 0;
        
        tableValidation[tableName] = { count, hasData };
        
        if (!hasData) {
          issues.push(`Table '${tableName}' is empty`);
        }
        
        // Validate specific tables
        if (tableName === 'tblpatientcomplaints' && count < 10) {
          issues.push(`Patient complaints table has unusually few records (${count})`);
        }
        
        if (tableName === 'tblmedicinemaster' && count < 100) {
          issues.push(`Medicine master table has unusually few records (${count})`);
        }
      }
      
      const isValid = issues.length === 0;
      
      if (isValid) {
        console.log('✅ Data validation passed');
      } else {
        console.warn('⚠️ Data validation found issues:', issues);
      }
      
      return {
        isValid,
        issues,
        tableValidation
      };
      
    } catch (error: any) {
      issues.push(`Validation failed: ${error.message}`);
      return {
        isValid: false,
        issues,
        tableValidation
      };
    }
  }

  /**
   * Get sample data from each table for testing
   */
  async getSampleData(): Promise<{ [tableName: string]: any[] }> {
    console.log('📋 Getting sample data from all tables...');
    
    const sampleData: { [tableName: string]: any[] } = {};
    const availableTables = this.pouchService.getAvailableTables();
    
    for (const tableConfig of availableTables) {
      try {
        const data = await this.pouchService.getAllFromTable(tableConfig.name);
        sampleData[tableConfig.name] = data.slice(0, 5); // First 5 items
      } catch (error) {
        console.warn(`Failed to get sample data for ${tableConfig.name}:`, error);
        sampleData[tableConfig.name] = [];
      }
    }
    
    return sampleData;
  }

  /**
   * Export data statistics as JSON
   */
  async exportStats(): Promise<string> {
    const stats = await this.remedinovaData.getDatabaseStats();
    const validation = await this.validateData();
    
    const exportData = {
      timestamp: new Date().toISOString(),
      totalTables: Object.keys(stats).length,
      totalDocuments: Object.values(stats).reduce(
        (total: number, info: any) => total + (info.doc_count || 0), 
        0
      ),
      validation,
      tableStats: stats
    };
    
    return JSON.stringify(exportData, null, 2);
  }
}
