/* Electron-specific CSS fixes for scroll behavior */

/* Force proper viewport handling in Electron */
html {
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

body {
  overflow: hidden;
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
}

/* Ensure ion-app takes full height */
ion-app {
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

/* Fix ion-content scrolling in Electron */
ion-content {
  --overflow: auto !important;
  overflow-y: auto !important;
  overflow-x: auto !important;
  height: 100% !important;
  /* Enable hardware acceleration */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* Smooth scrolling */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Fix for ion-content inner scroll area */
ion-content .inner-scroll {
  overflow-y: auto !important;
  overflow-x: auto !important;
  height: 100%;
}

/* Ensure scrollable areas work properly */
.scroll-content,
.ion-content-scroll-host {
  overflow-y: auto !important;
  overflow-x: auto !important;
  height: 100%;
  -webkit-overflow-scrolling: touch;
}

/* Fix for nested scrollable containers */
.main-content,
.dashboard-grid,
.scroll-tabs-wrapper,
.tab-content {
  overflow-y: auto !important;
  overflow-x: auto !important;
  /* Ensure proper height calculation */
  min-height: 0;
  flex: 1;
  /* Hardware acceleration */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: scroll-position;
}

/* Fix for horizontal scrolling segments */
ion-segment[scrollable] {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  white-space: nowrap !important;
  -webkit-overflow-scrolling: touch;
  display: inline-flex !important;
  flex-wrap: nowrap !important;
  min-width: max-content !important;
  width: auto !important;
  /* Ensure visibility in Electron */
  opacity: 1 !important;
  visibility: visible !important;
  position: relative !important;
}

/* Fix for scroll tabs wrapper specifically */
.scroll-tabs-wrapper {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  white-space: nowrap !important;
  -webkit-overflow-scrolling: touch !important;
  display: block !important;
  width: 100% !important;
  /* Show scrollbars in Electron */
  scrollbar-width: thin !important;
  -ms-overflow-style: auto !important;
}

.scroll-tabs-wrapper::-webkit-scrollbar {
  height: 8px !important;
  display: block !important;
  background: transparent !important;
}

.scroll-tabs-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 4px !important;
}

.scroll-tabs-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 4px !important;
}

.scroll-tabs-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
}

/* Fix for ion-segment-button visibility */
ion-segment-button {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  min-width: 158px !important;
  position: relative !important;
  /* Ensure text is visible in Electron */
  color: #333 !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  background: transparent !important;

  /* Fix button native element */
  .button-native {
    color: #333 !important;
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    display: block !important;
  }

  /* Active state */
  &.segment-button-checked {
    color: #007bff !important;
    background: rgba(0, 123, 255, 0.1) !important;

    .button-native {
      color: #007bff !important;
    }
  }
}

/* Ensure proper rendering of scrollable content */
.scrollable-content {
  overflow-y: auto !important;
  overflow-x: auto !important;
  height: 100%;
  max-height: 100%;
}

/* Fix for cards and containers that should scroll */
ion-card {
  overflow: visible;
}

/* Electron-specific performance optimizations */
* {
  /* Improve rendering performance */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Fix for any fixed positioning issues in Electron */
.fixed-content {
  position: fixed;
  z-index: 1000;
}

/* Ensure proper z-index stacking */
ion-content {
  z-index: 1;
}

/* Fix for modal and overlay scrolling */
ion-modal,
ion-popover,
ion-action-sheet {
  .ion-content-scroll-host {
    overflow-y: auto !important;
  }
}

/* Electron-specific tab content fixes */
.tab-content {
  /* Force repaint for tab switching */
  &[data-selected-tab] {
    animation: tabContentUpdate 0.1s ease-in-out;
  }
}

@keyframes tabContentUpdate {
  0% { opacity: 0.99; }
  100% { opacity: 1; }
}

/* Force Angular change detection in Electron */
ion-segment {
  /* Trigger repaint on change */
  &[ng-reflect-value] {
    animation: segmentUpdate 0.1s ease-in-out;
  }
}

@keyframes segmentUpdate {
  0% { transform: translateZ(0); }
  100% { transform: translateZ(0); }
}

/* Electron-specific fixes for Ionic components */
ion-card {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  margin: 16px !important;
  overflow: hidden !important;
}

ion-button {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

ion-input {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

ion-checkbox {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

ion-list {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

ion-item {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix for Ionic component rendering in Electron */
ion-app {
  display: block !important;
  height: 100vh !important;
  width: 100vw !important;
}

/* Ensure all Ionic components are visible */
[class*="ion-"] {
  opacity: 1 !important;
  visibility: visible !important;
}
