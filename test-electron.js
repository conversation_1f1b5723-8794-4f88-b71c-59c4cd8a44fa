const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

console.log('Starting Electron test...');
console.log('Current directory:', __dirname);

// Check if www/index.html exists
const indexPath = path.join(__dirname, 'www', 'index.html');
console.log('Looking for index.html at:', indexPath);
console.log('File exists:', fs.existsSync(indexPath));

if (fs.existsSync(indexPath)) {
  console.log('index.html found, reading first few lines...');
  const content = fs.readFileSync(indexPath, 'utf8');
  console.log('First 200 characters:', content.substring(0, 200));
}

function createWindow() {
  console.log('Creating window...');
  
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    },
    show: true,
    backgroundColor: '#ffffff'
  });

  console.log('Window created, opening dev tools...');
  mainWindow.webContents.openDevTools();

  console.log('Loading file:', indexPath);
  mainWindow.loadFile(indexPath).then(() => {
    console.log('File loaded successfully');
  }).catch(err => {
    console.error('Failed to load file:', err);
  });

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Page finished loading');
  });

  mainWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription) => {
    console.error('Page failed to load:', errorCode, errorDescription);
  });
}

app.whenReady().then(() => {
  console.log('Electron app ready');
  createWindow();
});

app.on('window-all-closed', () => {
  console.log('All windows closed');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
