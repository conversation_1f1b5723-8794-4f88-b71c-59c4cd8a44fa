* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #cde7fa;
  line-height: 1.5;
}

html, body {
  height: 100%;
  overflow-y: auto;
}

// Loading and error states
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  min-height: 60vh;

  h2 {
    margin: 1rem 0 0.5rem 0;
    color: var(--ion-color-primary);
    font-weight: 600;
  }

  p {
    color: var(--ion-color-medium);
    margin-bottom: 2rem;
    max-width: 300px;
    line-height: 1.5;
  }

  ion-progress-bar {
    width: 200px;
    margin-top: 1rem;
  }

  .error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }

  .diagnostic-info {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(var(--ion-color-primary-rgb), 0.1);
    border-radius: 8px;

    p {
      margin: 0.25rem 0;
      font-size: 0.85rem;
    }
  }

  .error-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .debug-info {
    margin-top: 2rem;
    padding: 1rem;
    background: rgba(var(--ion-color-warning-rgb), 0.1);
    border-radius: 8px;
    max-width: 400px;

    h4, h5 {
      margin: 0 0 0.5rem 0;
      font-size: 0.9rem;
      color: var(--ion-color-warning);
    }

    h5 {
      font-size: 0.8rem;
      margin-top: 1rem;
    }

    p {
      margin: 0.25rem 0;
      font-size: 0.75rem;
      color: var(--ion-color-medium);
    }

    .error-details {
      margin-top: 1rem;

      ul {
        margin: 0.5rem 0;
        padding-left: 1rem;

        li {
          font-size: 0.7rem;
          color: var(--ion-color-danger);
          margin: 0.25rem 0;
          line-height: 1.3;
        }
      }
    }
  }
}

.success-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  margin: 1rem;
  background: rgba(var(--ion-color-success-rgb), 0.1);
  border-radius: 8px;
  border-left: 4px solid var(--ion-color-success);

  ion-icon {
    font-size: 1.2rem;
  }

  span {
    font-weight: 500;
    color: var(--ion-color-success);

    &.stats {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      margin-left: 0.5rem;
    }
  }
}

.home-container {
  --background: #ffffff;
}

// Responsive design
@media (max-width: 768px) {
  .loading-container, .error-container {
    padding: 2rem 1rem;

    .error-actions {
      flex-direction: column;
      width: 100%;
      max-width: 200px;
    }

    .debug-info {
      max-width: 100%;
    }
  }

  .success-banner {
    margin: 0.5rem;
    flex-direction: column;
    text-align: center;

    .stats {
      margin-left: 0 !important;
      margin-top: 0.25rem;
    }
  }
}
