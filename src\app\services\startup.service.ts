import { Injectable } from '@angular/core';
import { DataTestService } from './data-test.service';

export interface StartupResult {
  success: boolean;
  message: string;
  details: any;
  errors: string[];
  warnings: string[];
}

@Injectable({
  providedIn: 'root'
})
export class StartupService {

  constructor(private dataTest: DataTestService) {}

  async initialize(): Promise<StartupResult> {
    console.log('🚀 Starting RemediNova application initialization...');
    
    const startTime = Date.now();
    const errors: string[] = [];
    const warnings: string[] = [];
    let details: any = {};

    try {
      // Step 1: Check environment
      console.log('📋 Step 1: Checking environment...');
      const envCheck = this.checkEnvironment();
      if (!envCheck.success) {
        errors.push(...envCheck.errors);
        warnings.push(...envCheck.warnings);
      }
      details.environment = envCheck.details;

      // Step 2: Run comprehensive diagnostics
      console.log('🔍 Step 2: Running diagnostics...');
      const diagnostics = await this.dataTest.runDiagnostics();
      if (!diagnostics.success) {
        errors.push(...diagnostics.errors);
      }
      warnings.push(...diagnostics.warnings);
      details.diagnostics = diagnostics.info;

      // Step 3: Performance check
      console.log('⚡ Step 3: Performance check...');
      const perfCheck = await this.performanceCheck();
      details.performance = perfCheck;
      if (perfCheck.loadTime > 30000) {
        warnings.push('Data loading is slower than expected (>30s)');
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      const success = errors.length === 0;
      const message = success 
        ? `✅ Application initialized successfully in ${totalTime}ms`
        : `❌ Application initialization failed with ${errors.length} errors`;

      console.log(message);
      
      if (warnings.length > 0) {
        console.warn(`⚠️ ${warnings.length} warnings:`, warnings);
      }

      return {
        success,
        message,
        details: {
          ...details,
          totalInitTime: totalTime,
          timestamp: new Date().toISOString()
        },
        errors,
        warnings
      };

    } catch (error: any) {
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      console.error('💥 Critical initialization error:', error);
      
      return {
        success: false,
        message: `💥 Critical initialization error: ${error.message}`,
        details: {
          ...details,
          totalInitTime: totalTime,
          timestamp: new Date().toISOString(),
          criticalError: error.message
        },
        errors: [error.message],
        warnings
      };
    }
  }

  private checkEnvironment(): { success: boolean; errors: string[]; warnings: string[]; details: any } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const details: any = {};

    try {
      // Check browser capabilities
      details.userAgent = navigator.userAgent;
      details.platform = navigator.platform;
      details.language = navigator.language;

      // Check IndexedDB support
      if (!window.indexedDB) {
        errors.push('IndexedDB is not supported in this browser');
      } else {
        details.indexedDBSupported = true;
      }

      // Check localStorage
      try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        details.localStorageSupported = true;
      } catch (e) {
        warnings.push('localStorage is not available');
        details.localStorageSupported = false;
      }

      // Check if running in development mode
      details.isDevelopment = !environment.production;

      // Check memory (if available)
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        details.memory = {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
        };
      }

      // Check connection (if available)
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        details.connection = {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        };
      }

      return {
        success: errors.length === 0,
        errors,
        warnings,
        details
      };

    } catch (error: any) {
      errors.push(`Environment check failed: ${error.message}`);
      return {
        success: false,
        errors,
        warnings,
        details
      };
    }
  }

  private async performanceCheck(): Promise<any> {
    const startTime = Date.now();
    
    try {
      // Quick performance test
      const quickTestResult = await this.dataTest.quickTest();
      const endTime = Date.now();
      
      return {
        loadTime: endTime - startTime,
        quickTestPassed: quickTestResult,
        timestamp: new Date().toISOString()
      };
      
    } catch (error: any) {
      const endTime = Date.now();
      
      return {
        loadTime: endTime - startTime,
        quickTestPassed: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  async getSystemInfo(): Promise<any> {
    return {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      hardwareConcurrency: navigator.hardwareConcurrency,
      maxTouchPoints: navigator.maxTouchPoints,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth
      },
      window: {
        innerWidth: window.innerWidth,
        innerHeight: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio
      }
    };
  }
}

// Simple environment check (you might need to adjust this based on your setup)
const environment = {
  production: !window.location.hostname.includes('localhost')
};
