import { Injectable } from '@angular/core';
import { PouchService } from './pouch.service';

export interface PatientComplaint {
  Id: string;
  ComplaintName: string;
  ReMeDi_Code?: string;
  Description?: string;
  IsDeleted: string;
  IsEnabled: string;
  domain: string;
}

export interface State {
  StateId: string;
  State: string;
  CountryId: string;
  IsDeleted: string;
  domain: string;
}

export interface Country {
  CountryId: string;
  Country: string;
  IsDeleted: string;
  domain: string;
}

export interface District {
  DistrictId: string;
  District: string;
  StateId: string;
  IsDeleted: string;
  domain: string;
}

export interface Medicine {
  Medicine_Id: string;
  Medicine_Name: string;
  Drug_Class_Id: string;
  Drug_Form_Id: string;
  IsDeleted: string;
  domain: string;
  Potential_Adverse_Effect?: string;
}

export interface Diagnosis {
  Dignosis_Id: string;
  Diagnosis_Name: string;
  ICD_CODE: string;
  CategoryId: string;
  IsDeleted: string;
  domain: string;
}

@Injectable({
  providedIn: 'root'
})
export class RemedinovaDataService {

  constructor(private pouchService: PouchService) {}

  /**
   * Initialize all databases and seed data if needed
   */
  async initialize(): Promise<void> {
    await this.pouchService.ensureSeededFromAssets();
  }

  // Patient Complaints Methods
  async getAllComplaints(): Promise<PatientComplaint[]> {
    return this.pouchService.getAllFromTable('tblpatientcomplaints');
  }

  async searchComplaints(searchText: string): Promise<PatientComplaint[]> {
    return this.pouchService.searchInTable('tblpatientcomplaints', searchText);
  }

  async getComplaintById(id: string): Promise<PatientComplaint | null> {
    return this.pouchService.getFromTable('tblpatientcomplaints', id);
  }

  // Location Methods
  async getAllStates(): Promise<State[]> {
    return this.pouchService.getAllFromTable('tblstate');
  }

  async getAllCountries(): Promise<Country[]> {
    return this.pouchService.getAllFromTable('tblcountry');
  }

  async getAllDistricts(): Promise<District[]> {
    return this.pouchService.getAllFromTable('tbldistrict');
  }

  async getDistrictsByState(stateId: string): Promise<District[]> {
    return this.pouchService.findInTable('tbldistrict', 'StateId', stateId);
  }

  async getAllBlocks(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tblblock');
  }

  async getBlocksByDistrict(districtId: string): Promise<any[]> {
    return this.pouchService.findInTable('tblblock', 'DistrictId', districtId);
  }

  async getAllVillages(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tblvillage');
  }

  async getVillagesByBlock(blockId: string): Promise<any[]> {
    return this.pouchService.findInTable('tblvillage', 'BlockId', blockId);
  }

  // Medicine Methods
  async getAllMedicines(): Promise<Medicine[]> {
    return this.pouchService.getAllFromTable('tblmedicinemaster');
  }

  async searchMedicines(searchText: string): Promise<Medicine[]> {
    return this.pouchService.searchInTable('tblmedicinemaster', searchText);
  }

  async getMedicineById(id: string): Promise<Medicine | null> {
    return this.pouchService.getFromTable('tblmedicinemaster', id);
  }

  async getAllDrugClasses(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tbldrug_class');
  }

  async getAllDrugForms(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tbldrug_form');
  }

  async getAllMedicationBrands(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tblmedicationbrand');
  }

  // Diagnosis Methods
  async getAllDiagnoses(): Promise<Diagnosis[]> {
    return this.pouchService.getAllFromTable('tbldiagnosismaster');
  }

  async searchDiagnoses(searchText: string): Promise<Diagnosis[]> {
    return this.pouchService.searchInTable('tbldiagnosismaster', searchText);
  }

  async getDiagnosisById(id: string): Promise<Diagnosis | null> {
    return this.pouchService.getFromTable('tbldiagnosismaster', id);
  }

  async getAllDiagnosisCategories(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tbldiagnosiscategory');
  }

  async getAllDiagnosisChapters(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tbldiagnosischapter');
  }

  async getAllDiagnosisSubchapters(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tbldiagnosissubchapter');
  }

  // Lab Methods
  async getAllLabCategories(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tbllabcategory');
  }

  async getAllLabSubtests(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tbllabsubtest');
  }

  async searchLabTests(searchText: string): Promise<any[]> {
    return this.pouchService.searchInTable('tbllabsubtest', searchText);
  }

  // Specialty Methods
  async getAllSpecialities(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tblspeciality_list');
  }

  async getAllReferralSpecialties(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tblreferralspecialtylist');
  }

  // Instructions Methods
  async getAllInstructions(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tblinstructions');
  }

  async getAllSpecialInstructions(): Promise<any[]> {
    return this.pouchService.getAllFromTable('tblspecialinstruction');
  }

  async searchInstructions(searchText: string): Promise<any[]> {
    return this.pouchService.searchInTable('tblinstructions', searchText);
  }

  // Utility Methods
  async getDatabaseStats(): Promise<{ [tableName: string]: PouchDB.Core.DatabaseInfo }> {
    return this.pouchService.getAllTablesInfo();
  }

  async clearAllData(): Promise<void> {
    await this.pouchService.clearAll();
  }

  async reinitializeData(): Promise<void> {
    await this.clearAllData();
    await this.initialize();
  }

  // Get location hierarchy configuration
  async getLocationHierarchy(): Promise<any> {
    return this.pouchService.getFromTable('locationhierarchy', 'locationhierarchy_config');
  }
}
