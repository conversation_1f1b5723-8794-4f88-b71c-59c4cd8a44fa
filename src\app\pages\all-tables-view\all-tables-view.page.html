<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>All RemediNova Tables</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="exportAllData()" [disabled]="isLoading">
        <ion-icon name="download-outline"></ion-icon>
      </ion-button>
      <ion-button (click)="loadAllTablesData()" [disabled]="isLoading">
        <ion-icon name="refresh-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">All Tables</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading all tables data...</p>
    <p class="loading-detail">This may take a moment for large datasets</p>
  </div>

  <div *ngIf="!isLoading">
    <!-- Summary Header -->
    <ion-card class="summary-card">
      <ion-card-header>
        <ion-card-title>Database Summary</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col size="4">
              <div class="summary-stat">
                <h2>{{ tableSummaries.length }}</h2>
                <p>Total Tables</p>
              </div>
            </ion-col>
            <ion-col size="4">
              <div class="summary-stat">
                <h2>{{ totalRecords | number }}</h2>
                <p>Total Records</p>
              </div>
            </ion-col>
            <ion-col size="4">
              <div class="summary-stat">
                <h2>{{ loadedTables }}</h2>
                <p>Loaded Tables</p>
              </div>
            </ion-col>
          </ion-row>
        </ion-grid>
        
        <ion-button 
          (click)="exportAllData()" 
          expand="block" 
          fill="outline" 
          class="export-all-btn">
          <ion-icon name="download-outline" slot="start"></ion-icon>
          Export All Data as JSON
        </ion-button>
      </ion-card-content>
    </ion-card>

    <!-- Tables Grid -->
    <div class="tables-grid">
      <ion-card 
        *ngFor="let table of tableSummaries; let i = index" 
        class="table-summary-card"
        [class]="'color-' + getTableColorClass(i)">
        
        <!-- Table Header -->
        <ion-card-header class="table-summary-header">
          <div class="table-title-section">
            <ion-icon [name]="getTableIcon(table.name)" class="table-icon"></ion-icon>
            <div class="table-info">
              <ion-card-title>{{ table.displayName }}</ion-card-title>
              <ion-card-subtitle>{{ table.name }}</ion-card-subtitle>
            </div>
          </div>
          <div class="table-stats">
            <div class="record-count">
              <span class="count">{{ table.count | number }}</span>
              <span class="label">records</span>
            </div>
            <ion-badge 
              [color]="table.isLoaded ? 'success' : 'warning'"
              class="status-badge">
              {{ table.isLoaded ? 'Loaded' : 'Error' }}
            </ion-badge>
          </div>
        </ion-card-header>

        <!-- Table Content -->
        <ion-card-content *ngIf="table.isLoaded && table.sampleData.length > 0" class="table-content">
          
          <!-- Sample Data Table -->
          <div class="sample-data-section">
            <h4>Sample Data (First 5 Records)</h4>
            
            <div class="mini-table-wrapper">
              <table class="mini-table">
                <thead>
                  <tr>
                    <th>#</th>
                    <th *ngFor="let column of getTableColumns(table.sampleData)">
                      {{ column }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let record of table.sampleData; let idx = index">
                    <td class="row-num">{{ idx + 1 }}</td>
                    <td *ngFor="let column of getTableColumns(table.sampleData)" 
                        [title]="getDisplayValue(record[column])">
                      {{ getDisplayValue(record[column]) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <div class="table-actions">
              <ion-button 
                (click)="exportTableData(table)" 
                fill="outline" 
                size="small">
                <ion-icon name="download-outline" slot="start"></ion-icon>
                Export
              </ion-button>
              
              <span class="sample-note">
                Showing {{ table.sampleData.length }} of {{ table.count | number }} records
              </span>
            </div>
          </div>
        </ion-card-content>

        <!-- No Data State -->
        <ion-card-content *ngIf="table.isLoaded && table.sampleData.length === 0" class="no-data-content">
          <div class="no-data-message">
            <ion-icon name="document-outline" class="no-data-icon"></ion-icon>
            <p>No data available</p>
          </div>
        </ion-card-content>

        <!-- Error State -->
        <ion-card-content *ngIf="!table.isLoaded" class="error-content">
          <div class="error-message">
            <ion-icon name="warning-outline" class="error-icon"></ion-icon>
            <p>Failed to load table data</p>
          </div>
        </ion-card-content>

      </ion-card>
    </div>

    <!-- Quick Stats Footer -->
    <ion-card class="stats-footer">
      <ion-card-content>
        <h3>Quick Statistics</h3>
        <ion-list>
          <ion-item *ngFor="let table of tableSummaries.slice(0, 10)">
            <ion-icon [name]="getTableIcon(table.name)" slot="start"></ion-icon>
            <ion-label>
              <h3>{{ table.displayName }}</h3>
              <p>{{ table.count | number }} records</p>
            </ion-label>
            <ion-badge slot="end" [color]="table.count > 1000 ? 'success' : table.count > 100 ? 'warning' : 'medium'">
              {{ table.count | number }}
            </ion-badge>
          </ion-item>
        </ion-list>
        
        <div *ngIf="tableSummaries.length > 10" class="more-tables">
          <p>... and {{ tableSummaries.length - 10 }} more tables</p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
