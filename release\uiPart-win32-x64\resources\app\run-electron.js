const { spawn } = require('child_process');
const path = require('path');

console.log('Building Angular app...');

// Build the app first
const buildProcess = spawn('ng', ['build'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

buildProcess.on('close', (code) => {
  if (code === 0) {
    console.log('Build successful, starting Electron...');
    
    // Start Electron
    const electronProcess = spawn('npx', ['electron', '.'], {
      cwd: __dirname,
      stdio: 'inherit',
      shell: true
    });
    
    electronProcess.on('close', (electronCode) => {
      console.log('Electron closed with code:', electronCode);
    });
    
    electronProcess.on('error', (err) => {
      console.error('Electron error:', err);
    });
    
  } else {
    console.error('Build failed with code:', code);
  }
});

buildProcess.on('error', (err) => {
  console.error('Build error:', err);
});
