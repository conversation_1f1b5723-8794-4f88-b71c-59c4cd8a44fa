import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar, IonInput } from '@ionic/angular/standalone';

@Component({
  selector: 'app-complaints',
  templateUrl: './complaints.page.html',
  styleUrls: ['./complaints.page.scss'],
  standalone: true,
  imports: [ IonInput, CommonModule, FormsModule,]
})
export class ComplaintsPage implements OnInit {

  constructor() { }

  ngOnInit() {
  }
otherComplaints = [
    { snomed: '271835004', icd: '-', text: 'Abdominal Swelling', since: '2 Hours' },
    { snomed: '125605004', icd: '-', text: 'Pain In The Fractured Part', since: '2 Days' },
    { snomed: '386861006, 82272006.', icd: '-', text: 'Fever And Cold', since: '5 Hours' },
  ];
}
