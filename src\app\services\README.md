# RemediNova Data Services

This directory contains services for managing the RemediNova API data using PouchDB in your Ionic application.

## Services Overview

### 1. PouchService (`pouch.service.ts`)
Low-level service that manages PouchDB databases for each table.

**Features:**
- Separate database for each table (better performance)
- Automatic data seeding from JSON file
- CRUD operations for all tables
- Search functionality
- Database statistics

### 2. RemedinovaDataService (`remedinova-data.service.ts`)
High-level service with typed methods for specific tables.

**Features:**
- Type-safe interfaces for major entities
- Convenient methods for common operations
- Search functionality for specific tables
- Location hierarchy management

### 3. DataLoaderService (`data-loader.service.ts`)
Utility service for managing data initialization.

**Features:**
- Ensures data is loaded only once
- Loading state management
- Error handling
- Performance monitoring

## Quick Start

### 1. Initialize Data in Your App

```typescript
// In your app.component.ts or main page
import { DataLoaderService } from './services/data-loader.service';

constructor(private dataLoader: DataLoaderService) {}

async ngOnInit() {
  try {
    await this.dataLoader.ensureDataLoaded();
    console.log('Data ready!');
  } catch (error) {
    console.error('Failed to load data:', error);
  }
}
```

### 2. Use RemedinovaDataService

```typescript
import { RemedinovaDataService } from './services/remedinova-data.service';

constructor(private remedinovaData: RemedinovaDataService) {}

// Get all patient complaints
async loadComplaints() {
  const complaints = await this.remedinovaData.getAllComplaints();
  console.log('Complaints:', complaints);
}

// Search medicines
async searchMedicines(searchText: string) {
  const results = await this.remedinovaData.searchMedicines(searchText);
  return results;
}

// Get location hierarchy
async loadStates() {
  const states = await this.remedinovaData.getAllStates();
  return states;
}
```

### 3. Use PouchService Directly (Advanced)

```typescript
import { PouchService } from './services/pouch.service';

constructor(private pouchService: PouchService) {}

// Get all data from any table
async getTableData(tableName: string) {
  return await this.pouchService.getAllFromTable(tableName);
}

// Search in any table
async searchTable(tableName: string, searchText: string) {
  return await this.pouchService.searchInTable(tableName, searchText);
}

// Get database statistics
async getStats() {
  return await this.pouchService.getAllTablesInfo();
}
```

## Available Tables

The system manages 22 tables from your RemediNovaAPI.json:

1. **locationhierarchy** - Location hierarchy configuration
2. **tblpatientcomplaints** - Patient complaints
3. **tblstate** - States
4. **tblcountry** - Countries
5. **tbldistrict** - Districts
6. **tblblock** - Blocks
7. **tblvillage** - Villages
8. **tblspeciality_list** - Medical specialties
9. **tblreferralspecialtylist** - Referral specialties
10. **tblinstructions** - Medical instructions
11. **tbllabcategory** - Lab categories
12. **tbllabsubtest** - Lab subtests
13. **tblspecialinstruction** - Special instructions
14. **tblmedicinemaster** - Medicines
15. **tblmedicationbrand** - Medication brands
16. **tbldrug_class** - Drug classes
17. **tbldrug_form** - Drug forms
18. **tbldiagnosiscategory** - Diagnosis categories
19. **tbldiagnosischapter** - Diagnosis chapters
20. **tbldiagnosismaster** - Diagnoses
21. **tbldiagnosissubchapter** - Diagnosis subchapters

## Common Use Cases

### Patient Complaint Selection
```typescript
// Load and search complaints
const complaints = await this.remedinovaData.searchComplaints('fever');
```

### Location Hierarchy
```typescript
// Get states, then districts for selected state
const states = await this.remedinovaData.getAllStates();
const districts = await this.remedinovaData.getDistrictsByState(selectedStateId);
const blocks = await this.remedinovaData.getBlocksByDistrict(selectedDistrictId);
```

### Medicine Search
```typescript
// Search medicines by name
const medicines = await this.remedinovaData.searchMedicines('paracetamol');
```

### Diagnosis Selection
```typescript
// Search diagnoses
const diagnoses = await this.remedinovaData.searchDiagnoses('diabetes');
```

## Performance Tips

1. **Data Loading**: Use `DataLoaderService` to ensure data is loaded only once
2. **Search**: Use the search methods instead of loading all data and filtering
3. **Caching**: The services automatically cache data in IndexedDB
4. **Batch Operations**: Use the bulk methods when possible

## Error Handling

```typescript
try {
  await this.dataLoader.ensureDataLoaded();
  const data = await this.remedinovaData.getAllComplaints();
} catch (error) {
  console.error('Data operation failed:', error);
  // Handle error appropriately
}
```

## Demo Page

Check `src/app/pages/data-demo/` for a complete example of how to use these services.
