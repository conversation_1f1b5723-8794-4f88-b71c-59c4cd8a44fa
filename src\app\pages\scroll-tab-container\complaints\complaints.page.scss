

.cards {
    padding: 16px;
}
input,
select {
  border: none;
  outline: none;
  box-shadow: none;
  background: transparent; /* Optional: if you want transparent background */
}
button{
  background-color: transparent;
}
.ion-inherit-color {
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 700;
    color: #4A4A48;
    transform: rotate(0deg);
    gap: 10px;
}
// ---------------------- complaint css -----------------
.ion-inherit-color {
  font-family: "DM Sans", sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #4A4A48;
  // width: 1289px;
  // height: 37px;
  transform: rotate(0deg);
  // opacity: 1;
  gap: 10px;
  // padding: 8px 16px;
}

.complaint-form-row {
  width: 87%;
  display: flex;
  gap:62px;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 27px;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.2px;
  line-height: 104%;



  input,
  select {
    flex: 1;
  }

  .btn-add {
    font-family: 'DM Sans', sans-serif;
    font-weight: 600;
    font-style: normal;
    /* 'SemiBold' is not a valid value; use font-weight for boldness */
    font-size: 14px;
    line-height: 140%;
    letter-spacing: 0.4px;
    text-align: center;
    vertical-align: middle;
    // width: 120px;
    height: 48px;
    text-transform: capitalize;
    position: relative;
    display: flex;
    gap: 8px;
    top: 12px;
    bottom: 12px;
    left: 24px;
    right: 24px;
    margin-top: 29px;
    margin-right: 24px;
    margin-left: -17px;
  }

  .btn-add span {
    font-family: 'DM Sans', sans-serif;
    font-weight: 600;
    font-style: normal;
    font-size: 14px;
    line-height: 140%;
    letter-spacing: 0.4px;
    text-align: center;
    vertical-align: middle;
    color: #007AFF;
  }

  .btn-add img{
    width: 24px;
    height: 24px;
  }
}

.complaint-form-row label {
  top: 10px;
  position: relative;
  right: 16px;
  bottom: 12px;
  left: 16px;
  //  width: 472.5px;
  height: 42px;
  display: block;
}

.complaint-input {
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  padding-left: 20px;
  width: 472.5px;
  height: 48px;
  /* margin-top: 9px; */
  top: 10px;
  position: relative;
  right: 16px;
  bottom: 12px;
  left: 16px;
}

.complaint-input-2 {
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  padding-left: 23px;
  width: 228.25px;
  height: 48px;
  /* margin-right: 15px; */
  /* margin-top: 9px; */
  gap: 10px;
  display: flex;
}

// .complaints-table css
.complaints-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  // border-radius: 10px;
  overflow: hidden;
  // box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.complaints-table th,
.complaints-table td {
  font-family: 'DM Sans', sans-serif;
  /* Ensure the font is imported */
  font-weight: 500;
  /* 500 = Medium */
  font-style: normal;
  /* 'Medium' is not valid here; use 'normal' */
  font-size: 14px;
  line-height: 150%;
  /* Or use 1.5 */
  letter-spacing: 0;
  /* Use 0 instead of 0% */
  // vertical-align: middle;
  height: 40px;
  // padding: 20px;
}

.complaints-table th {
  text-align: left;
  background-color: #D6E9FF;
  font-size: 12px;
  font-weight: 400;
  color: #374151;
}

.complaints-table tr {
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  font-weight: 400;
  color: #374151;
}

.complaints-table tr.highlight {
  background: #D1FAE5;
}

.complaints-table tr:last-child {
  border-bottom: none;
}

.complaints-table td {
  // vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 12px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 17px;
  color: #6c757d;
  transition: color 0.2s;
  height: 24px;
  width: 24px;
}

.action-icons .icon:hover {
  color: #1e90ff;
}

.complaints-table tr:nth-child(even) {
  background-color: #F9FAFB;
}

.complaints-table th:nth-child(1),
.complaints-table td:nth-child(1) {
  width: 180px;
  /* SNOMED CT */
}

.complaints-table th:nth-child(2),
.complaints-table td:nth-child(2) {
  width: 821px;
  /* Complaint Texting */
}

.complaints-table th:nth-child(3),
.complaints-table td:nth-child(3) {
  width: 150px;
  /* Since */
}

.complaints-table th:nth-child(4),
.complaints-table td:nth-child(4) {
  width: 58px;
  /* Action */
}

@media (max-width: 600px) {

  .complaints-table th,
  .complaints-table td {
    padding: 8px 6px;
    font-size: 13px;
  }
}
