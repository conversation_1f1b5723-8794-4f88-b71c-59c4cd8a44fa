const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    show: true
  });

  mainWindow.webContents.openDevTools();
  
  // Load the test HTML first
  mainWindow.loadFile('test.html').then(() => {
    console.log('Test HTML loaded');
    
    // After 3 seconds, load the actual app
    setTimeout(() => {
      console.log('Loading actual app...');
      mainWindow.loadFile(path.join(__dirname, 'www/index.html')).then(() => {
        console.log('App loaded');
      }).catch(err => {
        console.error('Failed to load app:', err);
      });
    }, 3000);
  });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
