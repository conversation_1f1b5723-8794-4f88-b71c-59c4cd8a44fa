import { Injectable } from '@angular/core';
import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';

// Enable pouchdb-find plugin
PouchDB.plugin(PouchDBFind);

export interface TableConfig {
  name: string;
  idField: string;
  displayField?: string;
}

@Injectable({ providedIn: 'root' })
export class PouchService {
  private databases: Map<string, PouchDB.Database> = new Map();
  private isInitialized = false;

  // Configuration for all tables
  private readonly tableConfigs: TableConfig[] = [
    { name: 'locationhierarchy', idField: 'id', displayField: 'label_1' },
    { name: 'tblpatientcomplaints', idField: 'Id', displayField: 'ComplaintName' },
    { name: 'tblstate', idField: 'StateId', displayField: 'State' },
    { name: 'tblcountry', idField: 'CountryId', displayField: 'Country' },
    { name: 'tbldistrict', idField: 'DistrictId', displayField: 'District' },
    { name: 'tblblock', idField: 'BlockId', displayField: 'Block' },
    { name: 'tblvillage', idField: 'VillageId', displayField: 'Village' },
    { name: 'tblspeciality_list', idField: 'Id', displayField: 'speciality' },
    { name: 'tblreferralspecialtylist', idField: 'ID', displayField: 'ReferralSpecialty_Name' },
    { name: 'tblinstructions', idField: 'id', displayField: 'instruction' },
    { name: 'tbllabcategory', idField: 'LabName_ID', displayField: 'LabName' },
    { name: 'tbllabsubtest', idField: 'TestId', displayField: 'TestName' },
    { name: 'tblspecialinstruction', idField: 'id', displayField: 'specialInstruction' },
    { name: 'tblmedicinemaster', idField: 'Medicine_Id', displayField: 'Medicine_Name' },
    { name: 'tblmedicationbrand', idField: 'Brand_Id', displayField: 'Medication_Brand' },
    { name: 'tbldrug_class', idField: 'Drug_Class_Id', displayField: 'Drug_Class' },
    { name: 'tbldrug_form', idField: 'Drug_Form_Id', displayField: 'Drug_Form' },
    { name: 'tbldiagnosiscategory', idField: 'CategoryId', displayField: 'Category' },
    { name: 'tbldiagnosischapter', idField: 'id', displayField: 'chapter' },
    { name: 'tbldiagnosismaster', idField: 'Dignosis_Id', displayField: 'Diagnosis_Name' },
    { name: 'tbldiagnosissubchapter', idField: 'id', displayField: 'subchapter' }
  ];

  constructor() {
    this.initializeDatabases();
  }

  /**
   * Initialize separate databases for each table
   */
  private initializeDatabases(): void {
    this.tableConfigs.forEach(config => {
      const dbName = `remedinova_${config.name}`;
      this.databases.set(config.name, new PouchDB(dbName));
    });
    this.isInitialized = true;
    console.log(`[PouchDB] Initialized ${this.tableConfigs.length} databases.`);
  }

  /**
   * Get database instance for a specific table
   */
  private getDatabase(tableName: string): PouchDB.Database {
    if (!this.isInitialized) {
      throw new Error('Databases not initialized');
    }
    const db = this.databases.get(tableName);
    if (!db) {
      throw new Error(`Database for table '${tableName}' not found`);
    }
    return db;
  }

  /**
   * Get table configuration
   */
  private getTableConfig(tableName: string): TableConfig {
    const config = this.tableConfigs.find(c => c.name === tableName);
    if (!config) {
      throw new Error(`Configuration for table '${tableName}' not found`);
    }
    return config;
  }

  /**
   * Ensures that initial JSON data is loaded and split into separate databases.
   * Only runs once per database (if no documents exist).
   */
  async ensureSeededFromAssets(): Promise<void> {
    console.log('[PouchDB] Checking if seeding is needed...');

    const res = await fetch('assets/data/RemediNovaAPI.json');
    const json = await res.json();

    if (!json.data || !Array.isArray(json.data)) {
      throw new Error('Invalid JSON structure: expected data array');
    }

    const seedingPromises: Promise<void>[] = [];

    // Process each table in the JSON data
    json.data.forEach((dataItem: any) => {
      for (const [tableName, tableData] of Object.entries(dataItem)) {
        const config = this.tableConfigs.find(c => c.name === tableName);
        if (!config) {
          console.warn(`[PouchDB] No configuration found for table: ${tableName}`);
          return;
        }

        seedingPromises.push(this.seedTable(tableName, tableData, config));
      }
    });

    await Promise.all(seedingPromises);
    console.log('[PouchDB] All tables seeded successfully.');
  }

  /**
   * Seed a specific table with data
   */
  private async seedTable(tableName: string, tableData: any, config: TableConfig): Promise<void> {
    const db = this.getDatabase(tableName);

    // Check if table is already seeded
    const info = await db.info();
    if (info.doc_count > 0) {
      console.log(`[PouchDB] Table '${tableName}' already seeded.`);
      return;
    }

    console.log(`[PouchDB] Seeding table '${tableName}'...`);

    const docs: PouchDB.Core.PutDocument<any>[] = [];

    if (Array.isArray(tableData)) {
      // Handle array data (most tables)
      tableData.forEach((item: any, index: number) => {
        const id = item[config.idField] ?? `${tableName}_${index + 1}`;
        docs.push({
          _id: `${id}`,
          ...item
        });
      });
    } else if (typeof tableData === 'object' && tableData !== null) {
      // Handle single object data (like locationhierarchy)
      docs.push({
        _id: `${tableName}_config`,
        ...tableData
      });
    }

    if (docs.length > 0) {
      const result = await db.bulkDocs(docs);
      const failed = result.filter((r: any) => r.error);
      if (failed.length > 0) {
        console.warn(`[PouchDB] Some docs failed to save in '${tableName}':`, failed);
      } else {
        console.log(`[PouchDB] Seeded ${docs.length} documents in '${tableName}'.`);
      }
    }
  }

  /**
   * Get all documents from a specific table
   */
  async getAllFromTable(tableName: string): Promise<any[]> {
    const db = this.getDatabase(tableName);
    const result = await db.allDocs({ include_docs: true });
    return result.rows.map(row => row.doc);
  }

  /**
   * Find documents in a table by field value
   */
  async findInTable(tableName: string, field: string, value: any): Promise<any[]> {
    const db = this.getDatabase(tableName);

    // Create index if it doesn't exist
    await db.createIndex({ index: { fields: [field] } });

    const result = await db.find({
      selector: { [field]: value },
      limit: 1000
    });

    return result.docs;
  }

  /**
   * Search documents in a table by text (for display fields)
   */
  async searchInTable(tableName: string, searchText: string): Promise<any[]> {
    const config = this.getTableConfig(tableName);
    const db = this.getDatabase(tableName);

    if (!config.displayField) {
      throw new Error(`No display field configured for table '${tableName}'`);
    }

    const result = await db.allDocs({ include_docs: true });
    const docs = result.rows.map(row => row.doc);

    // Simple text search (case-insensitive)
    return docs.filter(doc => {
      if (!doc) return false;
      const docAny = doc as any;
      const fieldValue = docAny[config.displayField!];
      return fieldValue &&
             fieldValue.toString().toLowerCase().includes(searchText.toLowerCase());
    });
  }

  /**
   * Get a specific document by ID from a table
   */
  async getFromTable(tableName: string, id: string): Promise<any> {
    const db = this.getDatabase(tableName);
    try {
      return await db.get(id);
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Add or update a document in a table
   */
  async saveToTable(tableName: string, doc: any): Promise<any> {
    const config = this.getTableConfig(tableName);
    const db = this.getDatabase(tableName);

    if (!doc._id && doc[config.idField]) {
      doc._id = doc[config.idField].toString();
    }

    return await db.put(doc);
  }

  /**
   * Delete a document from a table
   */
  async deleteFromTable(tableName: string, id: string): Promise<any> {
    const db = this.getDatabase(tableName);
    const doc = await db.get(id);
    return await db.remove(doc);
  }

  /**
   * Clear all documents from a specific table
   */
  async clearTable(tableName: string): Promise<void> {
    const db = this.getDatabase(tableName);
    const all = await db.allDocs({ include_docs: true });
    const deletions = all.rows.map((row: any) => ({
      _id: row.id,
      _rev: row.doc._rev,
      _deleted: true
    }));
    await db.bulkDocs(deletions);
    console.log(`[PouchDB] Cleared table '${tableName}'.`);
  }

  /**
   * Clear all documents from all tables
   */
  async clearAll(): Promise<void> {
    const clearPromises = this.tableConfigs.map(config => this.clearTable(config.name));
    await Promise.all(clearPromises);
    console.log('[PouchDB] Cleared all tables.');
  }

  /**
   * Get information about a specific table
   */
  async getTableInfo(tableName: string): Promise<PouchDB.Core.DatabaseInfo> {
    const db = this.getDatabase(tableName);
    return await db.info();
  }

  /**
   * Get information about all tables
   */
  async getAllTablesInfo(): Promise<{ [tableName: string]: PouchDB.Core.DatabaseInfo }> {
    const infoPromises = this.tableConfigs.map(async config => ({
      name: config.name,
      info: await this.getTableInfo(config.name)
    }));

    const results = await Promise.all(infoPromises);
    const infoMap: { [tableName: string]: PouchDB.Core.DatabaseInfo } = {};

    results.forEach(result => {
      infoMap[result.name] = result.info;
    });

    return infoMap;
  }

  /**
   * Get list of all available tables
   */
  getAvailableTables(): TableConfig[] {
    return [...this.tableConfigs];
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use getAllFromTable instead
   */
  async findByType(type: string): Promise<any[]> {
    console.warn(`findByType is deprecated. Use getAllFromTable('${type}') instead.`);
    return this.getAllFromTable(type);
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use getTableInfo instead
   */
  async getInfo(): Promise<PouchDB.Core.DatabaseInfo> {
    console.warn('getInfo is deprecated. Use getTableInfo(tableName) instead.');
    // Return info for the first table as fallback
    return this.getTableInfo(this.tableConfigs[0].name);
  }
}
