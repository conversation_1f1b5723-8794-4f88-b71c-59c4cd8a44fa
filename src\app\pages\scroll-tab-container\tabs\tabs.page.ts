
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Component, OnInit, ChangeDetectorRef, NgZone } from '@angular/core';
import { PatientHistoryPage } from '../patient-history/patient-history.page';
import { DiagnosisPage } from '../diagnosis/diagnosis.page';
import { ComplaintsPage } from '../complaints/complaints.page';
import { MedicinesPage } from '../medicines/medicines.page';
import { InterventionPage } from '../intervention/intervention.page';
import { InvestigationsPage } from '../investigations/investigations.page';
import { CounselingPage } from '../counseling/counseling.page';
import { ReferralsPage } from '../referrals/referrals.page';
import { PastrecordsPage } from '../pastrecords/pastrecords.page';



@Component({
  selector: 'app-tabs',
  templateUrl: './tabs.page.html',
  styleUrls: ['./tabs.page.scss'],
  standalone: true,
  imports: [IonicModule,  CommonModule, FormsModule, PatientHistoryPage,ComplaintsPage, DiagnosisPage, MedicinesPage, InterventionPage,InvestigationsPage, CounselingPage, ReferralsPage, PastrecordsPage]
})
export class TabsPage implements OnInit {


  // Add missing properties for diagnosis tab
  isProvisional: boolean = false;
  selectedDiagnosis: string = '';
  diagnoses: { code: string; name: string; provisional: boolean }[] = [
    { code: 'A42.1', name: 'Abdominal Actinomycosis', provisional: true },
    { code: 'A19.0', name: 'Acute Miliary Tuberculosis Of A Single Specified Site', provisional: true },
    { code: 'J00', name: 'Acute Nasopharyngitis [Common Cold]', provisional: false },
    { code: 'Y45.5', name: '4-Aminophenol Derivatives', provisional: false }
  ];

  addDiagnosis() {
    if (!this.selectedDiagnosis.trim()) {
      return;
    }
    const newDiagnosis = {
      code: this.generateICDCode(),
      name: this.selectedDiagnosis,
      provisional: this.isProvisional
    };
    this.diagnoses.push(newDiagnosis);
    this.selectedDiagnosis = '';
    this.isProvisional = false;
  }

  generateICDCode(): string {
    const code = 'X' + Math.floor(100 + Math.random() * 900);
    return code;
  }


 selectedTab: string = 'patientHistory';


 constructor(
  private ngZone: NgZone,
  private cdRef: ChangeDetectorRef
) {}

  ngOnInit() {
    // Optional: Initialization logic here
  }
switchTab(tab: string) {
  this.ngZone.run(() => {
    this.selectedTab = tab;
    this.cdRef.detectChanges();

    // Update active class on buttons
    setTimeout(() => {
      const buttons = document.querySelectorAll('.button-native');
      buttons.forEach(btn => btn.classList.remove('active'));

      const activeButton = document.querySelector(`[onclick*="${tab}"]`) ||
                          Array.from(buttons).find(btn =>
                            btn.textContent?.toLowerCase().replace(/\s+/g, '') ===
                            tab.toLowerCase().replace(/([A-Z])/g, ' $1').trim().replace(/\s+/g, '')
                          );

      if (activeButton) {
        activeButton.classList.add('active');
      }
    }, 10);
  });
}




  // --------------------------




// complaints-section.component.ts


  //test-card.component.ts
  // These @Input() properties should be in the child component, not here.

  // ------------------- parameters.page.ts
    physiologyTests = [
    { name: 'Temperature', value: '98.6°F', icon: 'thermometer', status: 'connected' },
    { name: 'Temperature (ASHA+)', value: '-', icon: 'thermometer', status: 'not-connected' },
    { name: 'Stethoscope', value: 'Normal', icon: 'stethoscope', status: 'connected' },
    { name: 'Stethoscope (ASHA+)', value: '-', icon: 'stethoscope', status: 'not-connected' },
    { name: 'Spirometer', value: '-', icon: 'pulse', status: 'not-connected' },
    { name: 'Blood Pressure', value: '120/80 mmHg', icon: 'water', status: 'connected' },
    { name: 'SpO2', value: '98%', icon: 'heart', status: 'connected' },
    { name: 'ECG', value: '-', icon: 'analytics', status: 'not-connected' },
    { name: 'ECG Interpretation', value: '-', icon: 'document-text', status: 'disabled' },
    { name: 'Fetal Doppler (FD)', value: '-', icon: 'body', status: 'not-connected' },
    { name: 'Fetal Doppler (Fetoscope)', value: '-', icon: 'body', status: 'not-connected' },
    { name: 'Auto Refractometer', value: '-', icon: 'eye', status: 'not-connected' },
    { name: 'X-Ray', value: '-', icon: 'image', status: 'not-connected' }
  ];

  bloodTests = [
    { name: 'Glucose', value: '-', icon: 'water', status: 'not-connected' },
    { name: 'Glucose (Wireless)', value: '-', icon: 'water', status: 'not-connected' },
    { name: 'Hemoglobin', value: '-', icon: 'water', status: 'not-connected' },
    { name: 'Lipid Profile', value: '-', icon: 'water', status: 'not-connected' },
    { name: 'Optical Reader', value: '-', icon: 'camera', status: 'not-connected' },
    { name: 'Urine Test', value: '-', icon: 'flask', status: 'not-connected' },
    { name: 'HbA1c (Wireless)', value: '-', icon: 'pulse', status: 'not-connected' },
    { name: 'WBC Differential', value: '-', icon: 'git-branch', status: 'not-connected' }
  ];




}
