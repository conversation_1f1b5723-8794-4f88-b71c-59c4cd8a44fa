/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import '@ionic/angular/css/palettes/dark.system.css';

/* Import Electron-specific fixes */
@import './electron-fixes.scss';

/* Import Electron-specific fixes */
@import './electron-fixes.scss';
/* Base styles for both web and Electron */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  /* Force hardware acceleration for better performance */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  background-color: white;
   --ion-color-scheme: light;
}
:root {
  color-scheme: light;
}



/* Ensure proper app container */
ion-app {
  height: 100vh !important;
  width: 100vw !important;
  position: relative;
}

/* Core ion-content scrolling */
ion-content {
  --overflow: auto !important;
  height: 100% !important;
  /* Enable momentum scrolling on webkit (Electron uses Chromium) */
  -webkit-overflow-scrolling: touch;
  /* Ensure proper rendering */
  will-change: scroll-position;
}

/* Ensure ion-content inner elements can scroll */
ion-content .inner-scroll {
  overflow-y: auto !important;
  overflow-x: auto !important;
  height: 100% !important;
}

/* Fix for Ionic pages */
.ion-page {
  height: 100vh !important;
  display: flex;
  flex-direction: column;
}

/* Ensure scrollable content areas work properly */
.scroll-content,
.ion-content-scroll-host {
  overflow-y: auto !important;
  overflow-x: auto !important;
  height: 100% !important;
  -webkit-overflow-scrolling: touch;
}

/* Fix for nested scrollable areas */
.main-content,
.dashboard-grid,
.tab-content {
  overflow-y: auto !important;
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch;
  /* Ensure proper height for scrolling */
  flex: 1;
  min-height: 0;
}

/* Specific fix for horizontal scrolling tabs */
.scroll-tabs-wrapper {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  -webkit-overflow-scrolling: touch;
  white-space: nowrap !important;
  display: block !important;
  width: 100% !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure ion-segment is visible and scrollable */
ion-segment[scrollable] {
  overflow-x: auto !important;
  overflow-y: hidden !important;
  white-space: nowrap !important;
  display: inline-flex !important;
  flex-wrap: nowrap !important;
  min-width: max-content !important;
  width: auto !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure segment buttons are visible */
ion-segment-button {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  min-width: 158px !important;
  /* Ensure text is visible */
  color: #333 !important;
  font-size: 16px !important;
  font-weight: 400 !important;

  /* Fix for button native element */
  .button-native {
    color: #333 !important;
    opacity: 1 !important;
    visibility: visible !important;
    font-size: 16px !important;
    font-weight: 400 !important;
  }

  /* Active state */
  &.segment-button-checked {
    color: #007bff !important;

    .button-native {
      color: #007bff !important;
    }
  }
}
