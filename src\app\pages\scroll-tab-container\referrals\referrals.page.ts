import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { firstValueFrom, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { PouchdbService, MasterData } from '../../../service/pouchdb.service';

interface Speciality {
  speciality: string;
  Id: string;
}


@Component({
  selector: 'app-referrals',
  templateUrl: './referrals.page.html',
  styleUrls: ['./referrals.page.scss','../tabs/tabs.page.scss','../medicines/medicines.page.scss', '../investigations/investigations.page.scss'],
  standalone: true,
  imports: [ CommonModule, FormsModule]
})

export class ReferralsPage implements OnInit {
  specialityList: Speciality[] = [];
  selectedSpeciality: string = '';
  loading = false;
  error = '';

  constructor(private http: HttpClient, private pouch: PouchdbService) {}

  async ngOnInit() {
    await this.loadSpecialities();
  }

  private async loadSpecialities() {
    this.loading = true;
    this.error = '';
    try {
      // 1. Try to get master data from PouchDB
      let masterData: MasterData;
      try {
        masterData = await firstValueFrom(
          this.pouch.getMasterData().pipe(
            catchError((e) => {
              // propagate so outer catches
              throw e;
            })
          )
        );
      } catch {
        // 2. Fallback: load from asset JSON
        const jsonData = await firstValueFrom(
          this.http.get<MasterData>('assets/data/RemediNovaAPI.json').pipe(
            catchError((httpErr) => {
              throw new Error('Failed to load master JSON from assets.');
            })
          )
        );
        masterData = jsonData;

        // 3. Attempt to save into PouchDB (non-fatal if it fails)
        try {
          await firstValueFrom(
            this.pouch.addOrUpdateMasterData(masterData).pipe(
              catchError(() => of(null)) // swallow save errors
            )
          );
        } catch {
          // ignore
        }
      }

      // 4. Extract tblspeciality_list from master data structure
      const rawList: any[] = this.extractTable(masterData, 'tblspeciality_list');
      this.specialityList = rawList
        .filter((s) => s && typeof s.speciality === 'string')
        .map((s) => ({
          speciality: s.speciality,
          Id: s.Id,
        }));

      if (this.specialityList.length === 0) {
        this.error = 'No specializations found in master data.';
      }
    } catch (err: any) {
      console.error('Error loading specialities:', err);
      this.error = 'Failed to load specializations.';
    } finally {
      this.loading = false;
    }
  }

  /** Handles both direct and wrapped-in-`data` array shapes */
  private extractTable(master: any, tableName: string): any[] {
    if (!master) return [];
    if (Array.isArray(master.data)) {
      for (const item of master.data) {
        if (item[tableName]) return item[tableName];
      }
    }
    if (master[tableName]) return master[tableName];
    return [];
  }

}
