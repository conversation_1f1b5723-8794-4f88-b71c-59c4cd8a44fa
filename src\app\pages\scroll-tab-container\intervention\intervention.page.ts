import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';


@Component({
  selector: 'app-intervention',
  templateUrl: './intervention.page.html',
  styleUrls: ['./intervention.page.scss', '../tabs/tabs.page.scss'],
  standalone: true,
  imports: [ CommonModule, FormsModule,IonicModule,]
})
export class InterventionPage implements OnInit {

  constructor() { }

  ngOnInit() {
  }
  // <!-- --------------- intervention tab -->
intervention = {
  treatmentPlan: '',
  clinicalObservations: '',
  followUpDate: null
};

}
