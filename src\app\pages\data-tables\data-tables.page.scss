// Loading container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  
  ion-spinner {
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--ion-color-medium);
    font-size: 0.9rem;
  }
}

// Stats card
.stats-card {
  margin: 1rem;
  
  .stat-item {
    text-align: center;
    padding: 0.5rem;
    
    h2 {
      font-size: 2rem;
      font-weight: 700;
      color: var(--ion-color-primary);
      margin: 0 0 0.25rem 0;
    }
    
    p {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
  
  .load-all-btn {
    margin-top: 1rem;
  }
}

// Tables container
.tables-container {
  padding: 0 1rem 1rem 1rem;
}

// Table cards
.table-card {
  margin-bottom: 1rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .table-header {
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: var(--ion-color-light);
    }
    
    &.clickable {
      user-select: none;
    }
    
    .table-header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .table-info {
        display: flex;
        align-items: center;
        flex: 1;
        
        .table-icon {
          font-size: 1.5rem;
          color: var(--ion-color-primary);
          margin-right: 1rem;
        }
        
        .table-details {
          flex: 1;
          
          ion-card-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
          }
          
          ion-card-subtitle {
            font-size: 0.85rem;
            color: var(--ion-color-medium);
            
            .loaded-indicator {
              color: var(--ion-color-success);
              font-weight: 500;
            }
          }
        }
      }
      
      .table-actions {
        display: flex;
        align-items: center;
        
        .small-spinner {
          width: 20px;
          height: 20px;
        }
        
        .expand-icon {
          font-size: 1.2rem;
          color: var(--ion-color-medium);
          transition: transform 0.2s ease;
        }
      }
    }
  }
  
  .table-content {
    padding: 0 1rem 1rem 1rem;
    
    .search-container {
      margin-bottom: 1rem;
      
      .table-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.5rem;
        
        .record-count {
          font-size: 0.85rem;
          color: var(--ion-color-medium);
        }
      }
    }
  }
}

// Data table styles
.table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid var(--ion-color-light);
  
  .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    
    thead {
      background-color: var(--ion-color-light);
      
      th {
        padding: 0.75rem 0.5rem;
        text-align: left;
        font-weight: 600;
        color: var(--ion-color-dark);
        border-bottom: 2px solid var(--ion-color-medium);
        white-space: nowrap;
        
        &:first-child {
          width: 50px;
          text-align: center;
        }
      }
    }
    
    tbody {
      tr {
        border-bottom: 1px solid var(--ion-color-light);
        
        &:hover {
          background-color: var(--ion-color-light-tint);
        }
        
        &:last-child {
          border-bottom: none;
        }
      }
      
      td {
        padding: 0.5rem;
        vertical-align: top;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        
        &.row-number {
          text-align: center;
          font-weight: 500;
          color: var(--ion-color-medium);
          background-color: var(--ion-color-light-tint);
          width: 50px;
          max-width: 50px;
        }
      }
    }
  }
  
  .pagination-info {
    padding: 1rem;
    text-align: center;
    background-color: var(--ion-color-light-tint);
    border-top: 1px solid var(--ion-color-light);
    
    ion-note {
      font-size: 0.8rem;
    }
  }
}

// No data states
.no-data, .no-results {
  text-align: center;
  padding: 2rem;
  color: var(--ion-color-medium);
  
  .no-data-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }
  
  p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .stats-card {
    margin: 0.5rem;
    
    .stat-item {
      padding: 0.25rem;
      
      h2 {
        font-size: 1.5rem;
      }
      
      p {
        font-size: 0.75rem;
      }
    }
  }
  
  .tables-container {
    padding: 0 0.5rem 0.5rem 0.5rem;
  }
  
  .table-card {
    .table-header {
      padding: 0.75rem;
      
      .table-header-content {
        .table-info {
          .table-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
          }
          
          .table-details {
            ion-card-title {
              font-size: 1rem;
            }
            
            ion-card-subtitle {
              font-size: 0.8rem;
            }
          }
        }
      }
    }
    
    .table-content {
      padding: 0 0.75rem 0.75rem 0.75rem;
    }
  }
  
  .data-table {
    font-size: 0.8rem;
    
    thead th {
      padding: 0.5rem 0.25rem;
    }
    
    tbody td {
      padding: 0.4rem 0.25rem;
      max-width: 150px;
    }
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .table-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .data-table {
    thead {
      background-color: var(--ion-color-dark);
      
      th {
        color: var(--ion-color-light);
        border-bottom-color: var(--ion-color-medium-shade);
      }
    }
    
    tbody {
      tr:hover {
        background-color: var(--ion-color-dark-tint);
      }
      
      td.row-number {
        background-color: var(--ion-color-dark-tint);
      }
    }
  }
  
  .pagination-info {
    background-color: var(--ion-color-dark-tint);
  }
}
