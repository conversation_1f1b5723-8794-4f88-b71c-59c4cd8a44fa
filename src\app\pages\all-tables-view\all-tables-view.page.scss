// Loading container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  
  ion-spinner {
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--ion-color-medium);
    text-align: center;
    margin: 0.5rem 0;
    
    &.loading-detail {
      font-size: 0.85rem;
      opacity: 0.8;
    }
  }
}

// Summary card
.summary-card {
  margin: 1rem;
  
  .summary-stat {
    text-align: center;
    padding: 0.5rem;
    
    h2 {
      font-size: 2.2rem;
      font-weight: 700;
      color: var(--ion-color-primary);
      margin: 0 0 0.25rem 0;
    }
    
    p {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
  
  .export-all-btn {
    margin-top: 1rem;
  }
}

// Tables grid
.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1rem;
  padding: 0 1rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding: 0 0.5rem;
  }
}

// Table summary cards
.table-summary-card {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  // Color variations
  &.color-primary {
    border-left: 4px solid var(--ion-color-primary);
  }
  
  &.color-secondary {
    border-left: 4px solid var(--ion-color-secondary);
  }
  
  &.color-tertiary {
    border-left: 4px solid var(--ion-color-tertiary);
  }
  
  &.color-success {
    border-left: 4px solid var(--ion-color-success);
  }
  
  &.color-warning {
    border-left: 4px solid var(--ion-color-warning);
  }
  
  &.color-danger {
    border-left: 4px solid var(--ion-color-danger);
  }
  
  .table-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    
    .table-title-section {
      display: flex;
      align-items: center;
      flex: 1;
      
      .table-icon {
        font-size: 1.8rem;
        color: var(--ion-color-primary);
        margin-right: 1rem;
      }
      
      .table-info {
        ion-card-title {
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }
        
        ion-card-subtitle {
          font-size: 0.8rem;
          color: var(--ion-color-medium);
          font-family: 'Courier New', monospace;
        }
      }
    }
    
    .table-stats {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 0.5rem;
      
      .record-count {
        text-align: right;
        
        .count {
          font-size: 1.4rem;
          font-weight: 700;
          color: var(--ion-color-primary);
        }
        
        .label {
          font-size: 0.75rem;
          color: var(--ion-color-medium);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-left: 0.25rem;
        }
      }
      
      .status-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
      }
    }
  }
  
  .table-content {
    padding: 0 1rem 1rem 1rem;
    
    .sample-data-section {
      h4 {
        font-size: 0.9rem;
        color: var(--ion-color-medium);
        margin: 0 0 0.75rem 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .table-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.75rem;
        
        .sample-note {
          font-size: 0.8rem;
          color: var(--ion-color-medium);
        }
      }
    }
  }
}

// Mini table styles
.mini-table-wrapper {
  overflow-x: auto;
  border-radius: 6px;
  border: 1px solid var(--ion-color-light);
  margin-bottom: 0.75rem;
  
  .mini-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.75rem;
    
    thead {
      background-color: var(--ion-color-light);
      
      th {
        padding: 0.5rem 0.4rem;
        text-align: left;
        font-weight: 600;
        color: var(--ion-color-dark);
        white-space: nowrap;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        
        &:first-child {
          width: 30px;
          text-align: center;
        }
      }
    }
    
    tbody {
      tr {
        border-bottom: 1px solid var(--ion-color-light-shade);
        
        &:hover {
          background-color: var(--ion-color-light-tint);
        }
        
        &:last-child {
          border-bottom: none;
        }
      }
      
      td {
        padding: 0.4rem;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        
        &.row-num {
          text-align: center;
          font-weight: 500;
          color: var(--ion-color-medium);
          background-color: var(--ion-color-light-tint);
          width: 30px;
        }
      }
    }
  }
}

// No data and error states
.no-data-content, .error-content {
  padding: 2rem 1rem;
  text-align: center;
  
  .no-data-message, .error-message {
    color: var(--ion-color-medium);
    
    .no-data-icon, .error-icon {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      opacity: 0.6;
    }
    
    .error-icon {
      color: var(--ion-color-warning);
    }
    
    p {
      font-size: 0.85rem;
      margin: 0;
    }
  }
}

// Stats footer
.stats-footer {
  margin: 1rem;
  
  h3 {
    font-size: 1.1rem;
    color: var(--ion-color-primary);
    margin-bottom: 1rem;
  }
  
  ion-list {
    background: transparent;
    
    ion-item {
      --padding-start: 0;
      --inner-padding-end: 0;
      
      ion-label {
        h3 {
          font-size: 0.9rem;
          font-weight: 500;
        }
        
        p {
          font-size: 0.8rem;
          color: var(--ion-color-medium);
        }
      }
    }
  }
  
  .more-tables {
    text-align: center;
    margin-top: 1rem;
    
    p {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      font-style: italic;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .summary-card {
    margin: 0.5rem;
    
    .summary-stat {
      h2 {
        font-size: 1.8rem;
      }
      
      p {
        font-size: 0.8rem;
      }
    }
  }
  
  .table-summary-card {
    .table-summary-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
      
      .table-stats {
        align-self: stretch;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  
  .mini-table {
    font-size: 0.7rem;
    
    thead th {
      padding: 0.4rem 0.3rem;
      max-width: 80px;
    }
    
    tbody td {
      padding: 0.3rem;
      max-width: 80px;
    }
  }
  
  .stats-footer {
    margin: 0.5rem;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .table-summary-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }
  }
  
  .mini-table {
    thead {
      background-color: var(--ion-color-dark);
      
      th {
        color: var(--ion-color-light);
      }
    }
    
    tbody {
      tr:hover {
        background-color: var(--ion-color-dark-tint);
      }
      
      td.row-num {
        background-color: var(--ion-color-dark-tint);
      }
    }
  }
}
