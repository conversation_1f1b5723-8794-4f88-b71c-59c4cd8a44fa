// ---------------- diggonosis css -------------
.cards {
    padding: 16px;
}
.ion-inherit-color {
    font-family: "DM Sans", sans-serif;
    font-size: 14px;
    font-weight: 700;
    color: #4A4A48;
    transform: rotate(0deg);
    gap: 10px;
}
.diagnosis-card {
  // padding: 16px;
}

ion-card-content {
  // margin-top: 30px;
}

.diagnosis-header {
  margin-top: 16px;
  padding: 5px;
  height: 17px;
  display: block;
  font-size: 16px;
    margin-bottom: 30px;
    font-weight: 400;
  font-family: "DM Sans", sans-serif;

}

.select-diagnosis {

  display: flex;
  width: 1052px;
  justify-content: space-between;

}

.item-inner {
  border: none !important;
}

.select-diagnosis span {

  font-size: 14px;
  width: 100%;
  margin-top: 16px;
  color: #111827;
}

.dig-input-contain {
  display: flex;
}

.dig-input-contain span {
  width: 98px;
  padding: 9px;
  margin-left: 25px;
  font-size: 17px;
  color: #007AFF;
  font-weight: 600;
}

.diagnosis-input {
  width: 1052px;
  background: transparent;
  border: 1px solid #D1D5DB;
  border-radius: 9px;
  font-size: 12px;
  font-weight: 500;
  font-family: "DM Sans", sans-serif;
  color: #4A4A48;
  padding: 0px 8px 0 8px;
}

.diagnosis-segment {
  margin-bottom: 16px;
}

.diagnosis-input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  margin-top: 39px;
}

.diagnosis-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  justify-content: space-between;
}
.diagnosis-checkbox label{
 font-size: 16px;
  font-weight: 400;
  font-family: "DM Sans", sans-serif;

}

//table css
/* New Table Styles */
.new-complaints-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
  font-family: 'Inter', Arial, sans-serif;
}

.new-complaints-table th,
.new-complaints-table td {
  text-align: left;
  font-size: 14px;
}

.new-complaints-table th {
  background: #D6E9FF;
  font-weight: 400;
  color: #374151;
  font-size: 12px;
  height: 33px;
}

.new-complaints-table tr {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.new-complaints-table tr:last-child {
  border-bottom: none;
}

.new-complaints-table td {
  vertical-align: middle;
}

.action-icons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-right: 24px;
}

.action-icons .icon {
  cursor: pointer;
  font-size: 16px;
  color: #6c757d;
  transition: color 0.2s;
}

.action-icons .icon:hover {
  color: #1976d2;
}

@media (max-width: 600px) {

  .new-complaints-table th,
  .new-complaints-table td {
    padding: 7px 4px;
    font-size: 12px;
  }
}

.new-complaints-table th:nth-child(1),
.new-complaints-table td:nth-child(1) {
  width: 100px;
  padding-left: 4px;
}

.new-complaints-table th:nth-child(2),
.new-complaints-table td:nth-child(2) {
  width: 1289px;
}

.new-complaints-table th:nth-child(3),
.new-complaints-table td:nth-child(3) {
  width: 80px;
}

.new-complaints-table th:nth-child(4),
.new-complaints-table td:nth-child(4) {
  width: 58px;
}

.new-complaints-table tr:nth-child(even) {
  background-color: #F9FAFB;
}


