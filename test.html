<!DOCTYPE html>
<html>
<head>
    <title>Electron Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .scrollable {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <h1>Electron Scroll Test</h1>
    
    <div class="test-content">
        <h2>Basic Content</h2>
        <p>This is a test to verify that Electron is working properly.</p>
    </div>
    
    <div class="test-content">
        <h2>Scrollable Content</h2>
        <div class="scrollable">
            <p>This is scrollable content. Let's add enough text to make it scroll.</p>
            <p>Line 1 of scrollable content</p>
            <p>Line 2 of scrollable content</p>
            <p>Line 3 of scrollable content</p>
            <p>Line 4 of scrollable content</p>
            <p>Line 5 of scrollable content</p>
            <p>Line 6 of scrollable content</p>
            <p>Line 7 of scrollable content</p>
            <p>Line 8 of scrollable content</p>
            <p>Line 9 of scrollable content</p>
            <p>Line 10 of scrollable content</p>
            <p>Line 11 of scrollable content</p>
            <p>Line 12 of scrollable content</p>
            <p>Line 13 of scrollable content</p>
            <p>Line 14 of scrollable content</p>
            <p>Line 15 of scrollable content</p>
        </div>
    </div>
    
    <script>
        console.log('Test HTML loaded successfully');
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM Content Loaded');
        });
    </script>
</body>
</html>
